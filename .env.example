# Database Configuration
DB_HOST=localhost
DB_NAME=cryptoinvest
DB_USER=root
DB_PASS=

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRY=86400

# Application Configuration
APP_URL=http://localhost
APP_ENV=development

# Email Configuration
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=CryptoInvest

# Cryptocurrency API Configuration
CRYPTO_API_KEY=your_api_key
CRYPTO_API_URL=https://api.example.com
