<?php

namespace App\Config;

class App {
    // Application configuration
    public static $appUrl;
    public static $appEnv;
    
    // JWT configuration
    public static $jwtSecret;
    public static $jwtExpiry;
    
    // Email configuration
    public static $mailHost;
    public static $mailPort;
    public static $mailUsername;
    public static $mailPassword;
    public static $mailEncryption;
    public static $mailFromAddress;
    public static $mailFromName;
    
    // Cryptocurrency API configuration
    public static $cryptoApiKey;
    public static $cryptoApiUrl;
    
    public static function init() {
        // Load application configuration
        self::$appUrl = $_ENV['APP_URL'] ?? 'http://localhost';
        self::$appEnv = $_ENV['APP_ENV'] ?? 'development';
        
        // Load JWT configuration
        self::$jwtSecret = $_ENV['JWT_SECRET'] ?? 'default_secret_key';
        self::$jwtExpiry = (int)($_ENV['JWT_EXPIRY'] ?? 86400);
        
        // Load email configuration
        self::$mailHost = $_ENV['MAIL_HOST'] ?? '';
        self::$mailPort = (int)($_ENV['MAIL_PORT'] ?? 587);
        self::$mailUsername = $_ENV['MAIL_USERNAME'] ?? '';
        self::$mailPassword = $_ENV['MAIL_PASSWORD'] ?? '';
        self::$mailEncryption = $_ENV['MAIL_ENCRYPTION'] ?? 'tls';
        self::$mailFromAddress = $_ENV['MAIL_FROM_ADDRESS'] ?? '';
        self::$mailFromName = $_ENV['MAIL_FROM_NAME'] ?? 'CryptoInvest';
        
        // Load cryptocurrency API configuration
        self::$cryptoApiKey = $_ENV['CRYPTO_API_KEY'] ?? '';
        self::$cryptoApiUrl = $_ENV['CRYPTO_API_URL'] ?? '';
    }
}
