<?php

namespace App\Controllers;

use App\Models\User;
use App\Models\Investment;
use App\Models\InvestmentPlan;
use App\Models\Transaction;
use App\Models\Earning;
use App\Helpers\ResponseHelper;
use App\Helpers\ValidationHelper;
use App\Middleware\AuthMiddleware;

class AdminController {
    private $userModel;
    private $investmentModel;
    private $planModel;
    private $transactionModel;
    private $earningModel;
    
    public function __construct() {
        $this->userModel = new User();
        $this->investmentModel = new Investment();
        $this->planModel = new InvestmentPlan();
        $this->transactionModel = new Transaction();
        $this->earningModel = new Earning();
    }
    
    public function getDashboard() {
        // Authenticate admin
        AuthMiddleware::authenticateAdmin();
        
        // Get statistics
        $userCount = $this->userModel->count();
        $activeUserCount = $this->userModel->countActiveUsers();
        
        $investmentStats = $this->investmentModel->getInvestmentStats();
        $transactionStats = $this->transactionModel->getTransactionStats();
        
        $totalEarnings = $this->earningModel->getTotalEarnings();
        
        // Get recent transactions
        $recentTransactions = $this->transactionModel->getRecentTransactions(5);
        
        // Get pending withdrawals
        $pendingWithdrawals = $this->transactionModel->getPendingWithdrawals();
        
        // Return response
        ResponseHelper::success([
            'stats' => [
                'users' => [
                    'total' => $userCount,
                    'active' => $activeUserCount
                ],
                'investments' => $investmentStats,
                'transactions' => $transactionStats,
                'earnings' => [
                    'total' => $totalEarnings
                ]
            ],
            'recent_transactions' => $recentTransactions,
            'pending_withdrawals' => $pendingWithdrawals
        ]);
    }
    
    public function getUsers() {
        // Authenticate admin
        AuthMiddleware::authenticateAdmin();
        
        // Get query parameters
        $limit = isset($_GET['limit']) ? (int) $_GET['limit'] : null;
        $offset = isset($_GET['offset']) ? (int) $_GET['offset'] : null;
        
        // Get users
        $users = $this->userModel->getAllUsers($limit, $offset);
        
        // Return response
        ResponseHelper::success($users);
    }
    
    public function getUser($id) {
        // Authenticate admin
        AuthMiddleware::authenticateAdmin();
        
        // Get user
        $user = $this->userModel->findById($id);
        
        if (!$user) {
            ResponseHelper::notFound('User not found');
        }
        
        // Return response
        ResponseHelper::success($user);
    }
    
    public function updateUser($id) {
        // Authenticate admin
        AuthMiddleware::authenticateAdmin();
        
        // Get user
        $user = $this->userModel->findById($id);
        
        if (!$user) {
            ResponseHelper::notFound('User not found');
        }
        
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'status' => 'required'
        ]);
        
        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }
        
        // Update user
        if (!$this->userModel->update($id, $data)) {
            ResponseHelper::error('Failed to update user', 500);
        }
        
        // Get updated user
        $updatedUser = $this->userModel->findById($id);
        
        // Return response
        ResponseHelper::success($updatedUser, 'User updated successfully');
    }
    
    public function createPlan() {
        // Authenticate admin
        AuthMiddleware::authenticateAdmin();
        
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'name' => 'required|max:100',
            'min_amount' => 'required|numeric',
            'roi_percentage' => 'required|numeric',
            'duration_days' => 'required|numeric'
        ]);
        
        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }
        
        // Calculate daily ROI
        $data['daily_roi'] = $data['roi_percentage'] / $data['duration_days'];
        
        // Create plan
        $planId = $this->planModel->createPlan($data);
        
        if (!$planId) {
            ResponseHelper::error('Failed to create investment plan', 500);
        }
        
        // Get created plan
        $plan = $this->planModel->findById($planId);
        
        // Return response
        ResponseHelper::success($plan, 'Investment plan created successfully', 201);
    }
    
    public function updatePlan($id) {
        // Authenticate admin
        AuthMiddleware::authenticateAdmin();
        
        // Get plan
        $plan = $this->planModel->findById($id);
        
        if (!$plan) {
            ResponseHelper::notFound('Investment plan not found');
        }
        
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'name' => 'required|max:100',
            'min_amount' => 'required|numeric',
            'roi_percentage' => 'required|numeric',
            'duration_days' => 'required|numeric'
        ]);
        
        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }
        
        // Calculate daily ROI
        $data['daily_roi'] = $data['roi_percentage'] / $data['duration_days'];
        
        // Update plan
        if (!$this->planModel->updatePlan($id, $data)) {
            ResponseHelper::error('Failed to update investment plan', 500);
        }
        
        // Get updated plan
        $updatedPlan = $this->planModel->findById($id);
        
        // Return response
        ResponseHelper::success($updatedPlan, 'Investment plan updated successfully');
    }
    
    public function deletePlan($id) {
        // Authenticate admin
        AuthMiddleware::authenticateAdmin();
        
        // Get plan
        $plan = $this->planModel->findById($id);
        
        if (!$plan) {
            ResponseHelper::notFound('Investment plan not found');
        }
        
        // Delete plan
        if (!$this->planModel->delete($id)) {
            ResponseHelper::error('Failed to delete investment plan', 500);
        }
        
        // Return response
        ResponseHelper::success(null, 'Investment plan deleted successfully');
    }
    
    public function getPendingWithdrawals() {
        // Authenticate admin
        AuthMiddleware::authenticateAdmin();
        
        // Get pending withdrawals
        $pendingWithdrawals = $this->transactionModel->getPendingWithdrawals();
        
        // Return response
        ResponseHelper::success($pendingWithdrawals);
    }
}
