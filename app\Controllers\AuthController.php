<?php

namespace App\Controllers;

use App\Models\User;
use App\Helpers\JwtHelper;
use App\Helpers\ResponseHelper;
use App\Helpers\ValidationHelper;

class AuthController {
    private $userModel;

    public function __construct() {
        $this->userModel = new User();
    }

    public function register() {
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'username' => 'required|alphaNum|min:3|max:50',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed'
        ]);

        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }

        // Check if username already exists
        if ($this->userModel->findByUsername($data['username'])) {
            ResponseHelper::error('Username already exists', 422);
        }

        // Check if email already exists
        if ($this->userModel->findByEmail($data['email'])) {
            ResponseHelper::error('Email already exists', 422);
        }

        // Create user
        $userId = $this->userModel->createUser([
            'username' => $data['username'],
            'email' => $data['email'],
            'password' => $data['password'],
            'status' => 'active'
        ]);

        if (!$userId) {
            ResponseHelper::error('Failed to create user', 500);
        }

        // Get created user
        $user = $this->userModel->findById($userId);

        // Generate JWT token
        $token = JwtHelper::generateToken(
            $user['id'],
            $user['username'],
            $user['email'],
            $user['role']
        );

        // Return response
        ResponseHelper::success([
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'role' => $user['role']
            ],
            'token' => $token
        ], 'User registered successfully', 201);
    }

    public function login() {
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'username' => 'required',
            'password' => 'required'
        ]);

        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }

        // Check if username exists
        $user = $this->userModel->findByUsername($data['username']);

        if (!$user) {
            // Try with email
            $user = $this->userModel->findByEmail($data['username']);

            if (!$user) {
                ResponseHelper::error('Invalid credentials', 401);
            }
        }

        // Verify password
        if (!password_verify($data['password'], $user['password'])) {
            ResponseHelper::error('Invalid credentials', 401);
        }

        // Check if user is active
        if ($user['status'] !== 'active') {
            ResponseHelper::error('Your account is not active', 403);
        }

        // Update last login
        $this->userModel->updateLastLogin($user['id']);

        // Generate JWT token
        $token = JwtHelper::generateToken(
            $user['id'],
            $user['username'],
            $user['email'],
            $user['role']
        );

        // Store token and user data in session
        $_SESSION['token'] = $token;
        $_SESSION['user'] = (object) [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'role' => $user['role'],
            'balance' => $user['balance']
        ];

        // Return response
        ResponseHelper::success([
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'role' => $user['role'],
                'balance' => $user['balance']
            ],
            'token' => $token
        ], 'Login successful');
    }

    public function logout() {
        // Clear session
        session_unset();
        session_destroy();

        // Return response
        ResponseHelper::success(null, 'Logout successful');
    }

    public function verifyEmail() {
        // Get token from request
        $token = $_GET['token'] ?? '';

        if (empty($token)) {
            ResponseHelper::error('Invalid verification token', 400);
        }

        // Find user by verification token
        $user = $this->userModel->findByVerificationToken($token);

        if (!$user) {
            ResponseHelper::error('Invalid verification token', 400);
        }

        // Verify user
        if (!$this->userModel->verifyUser($user['id'])) {
            ResponseHelper::error('Failed to verify email', 500);
        }

        // Return response
        ResponseHelper::success(null, 'Email verified successfully');
    }

    public function forgotPassword() {
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'email' => 'required|email'
        ]);

        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }

        // Check if email exists
        $user = $this->userModel->findByEmail($data['email']);

        if (!$user) {
            // Don't reveal that email doesn't exist
            ResponseHelper::success(null, 'If your email is registered, you will receive a password reset link');
        }

        // Generate reset token
        $token = bin2hex(random_bytes(32));
        $expiry = date('Y-m-d H:i:s', strtotime('+1 hour'));

        // Save reset token
        if (!$this->userModel->setResetToken($user['id'], $token, $expiry)) {
            ResponseHelper::error('Failed to generate reset token', 500);
        }

        // TODO: Send reset email

        // Return response
        ResponseHelper::success(null, 'If your email is registered, you will receive a password reset link');
    }

    public function resetPassword() {
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'token' => 'required',
            'password' => 'required|min:8|confirmed'
        ]);

        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }

        // Find user by reset token
        $user = $this->userModel->findByResetToken($data['token']);

        if (!$user) {
            ResponseHelper::error('Invalid or expired reset token', 400);
        }

        // Update password
        if (!$this->userModel->updatePassword($user['id'], $data['password'])) {
            ResponseHelper::error('Failed to update password', 500);
        }

        // Return response
        ResponseHelper::success(null, 'Password reset successfully');
    }
}
