<?php

namespace App\Controllers;

use App\Models\User;
use App\Models\Investment;
use App\Models\InvestmentPlan;
use App\Models\Transaction;
use App\Models\Earning;
use App\Helpers\ResponseHelper;
use App\Helpers\ValidationHelper;
use App\Middleware\AuthMiddleware;

class InvestmentController {
    private $userModel;
    private $investmentModel;
    private $planModel;
    private $transactionModel;
    private $earningModel;
    
    public function __construct() {
        $this->userModel = new User();
        $this->investmentModel = new Investment();
        $this->planModel = new InvestmentPlan();
        $this->transactionModel = new Transaction();
        $this->earningModel = new Earning();
    }
    
    public function getPlans() {
        // Get active plans
        $plans = $this->planModel->getActivePlans();
        
        // Return response
        ResponseHelper::success($plans);
    }
    
    public function getPlan($id) {
        // Get plan
        $plan = $this->planModel->findById($id);
        
        if (!$plan) {
            ResponseHelper::notFound('Investment plan not found');
        }
        
        // Return response
        ResponseHelper::success($plan);
    }
    
    public function getUserInvestments() {
        // Authenticate user
        AuthMiddleware::authenticate();
        
        // Get user data
        $userId = $_REQUEST['user']->id;
        
        // Get investments
        $investments = $this->investmentModel->getUserInvestments($userId);
        
        // Return response
        ResponseHelper::success($investments);
    }
    
    public function getInvestment($id) {
        // Authenticate user
        AuthMiddleware::authenticate();
        
        // Get user data
        $userId = $_REQUEST['user']->id;
        
        // Get investment
        $investment = $this->investmentModel->findById($id);
        
        if (!$investment) {
            ResponseHelper::notFound('Investment not found');
        }
        
        // Check if investment belongs to user or user is admin
        if ($investment['user_id'] !== $userId && $_REQUEST['user']->role !== 'admin') {
            ResponseHelper::forbidden('You do not have permission to view this investment');
        }
        
        // Get investment earnings
        $earnings = $this->earningModel->getInvestmentEarnings($id);
        
        // Add earnings to investment data
        $investment['earnings'] = $earnings;
        
        // Return response
        ResponseHelper::success($investment);
    }
    
    public function createInvestment() {
        // Authenticate user
        AuthMiddleware::authenticate();
        
        // Get user data
        $userId = $_REQUEST['user']->id;
        $user = $this->userModel->findById($userId);
        
        if (!$user) {
            ResponseHelper::notFound('User not found');
        }
        
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'plan_id' => 'required|numeric',
            'amount' => 'required|numeric'
        ]);
        
        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }
        
        // Get plan
        $plan = $this->planModel->findById($data['plan_id']);
        
        if (!$plan) {
            ResponseHelper::notFound('Investment plan not found');
        }
        
        // Check if plan is active
        if ($plan['status'] !== 'active') {
            ResponseHelper::error('This investment plan is not available', 400);
        }
        
        // Validate amount
        if ($data['amount'] < $plan['min_amount']) {
            ResponseHelper::error('Minimum investment amount is ' . $plan['min_amount'], 422);
        }
        
        if ($plan['max_amount'] && $data['amount'] > $plan['max_amount']) {
            ResponseHelper::error('Maximum investment amount is ' . $plan['max_amount'], 422);
        }
        
        // Check if user has enough balance
        if ($user['balance'] < $data['amount']) {
            ResponseHelper::error('Insufficient balance', 422);
        }
        
        // Start transaction
        $this->userModel->conn->beginTransaction();
        
        try {
            // Deduct amount from user balance
            if (!$this->userModel->updateBalance($userId, -$data['amount'])) {
                throw new \Exception('Failed to update user balance');
            }
            
            // Create investment
            $investmentId = $this->investmentModel->createInvestment([
                'user_id' => $userId,
                'plan_id' => $data['plan_id'],
                'amount' => $data['amount'],
                'status' => 'active',
                'start_date' => date('Y-m-d H:i:s')
            ]);
            
            if (!$investmentId) {
                throw new \Exception('Failed to create investment');
            }
            
            // Create transaction record
            $transactionId = $this->transactionModel->createTransaction([
                'user_id' => $userId,
                'type' => 'investment',
                'amount' => $data['amount'],
                'status' => 'completed',
                'description' => 'Investment in ' . $plan['name']
            ]);
            
            if (!$transactionId) {
                throw new \Exception('Failed to create transaction record');
            }
            
            // Commit transaction
            $this->userModel->conn->commit();
            
            // Get created investment
            $investment = $this->investmentModel->findById($investmentId);
            
            // Return response
            ResponseHelper::success($investment, 'Investment created successfully', 201);
        } catch (\Exception $e) {
            // Rollback transaction
            $this->userModel->conn->rollBack();
            
            // Return error
            ResponseHelper::error($e->getMessage(), 500);
        }
    }
    
    public function calculateEarnings() {
        // Authenticate admin
        AuthMiddleware::authenticateAdmin();
        
        // Calculate daily earnings
        $earningsCreated = $this->earningModel->calculateDailyEarnings();
        
        // Return response
        ResponseHelper::success([
            'earnings_created' => $earningsCreated
        ], 'Earnings calculated successfully');
    }
}
