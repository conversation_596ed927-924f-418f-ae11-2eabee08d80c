<?php

namespace App\Controllers;

use App\Models\User;
use App\Models\Transaction;
use App\Helpers\ResponseHelper;
use App\Helpers\ValidationHelper;
use App\Middleware\AuthMiddleware;

class TransactionController {
    private $userModel;
    private $transactionModel;

    public function __construct() {
        $this->userModel = new User();
        $this->transactionModel = new Transaction();
    }

    public function getUserTransactions() {
        // Authenticate user
        AuthMiddleware::authenticate();

        // Get user data
        $userId = $_REQUEST['user']->id;

        // Get query parameters
        $type = $_GET['type'] ?? null;
        $status = $_GET['status'] ?? null;
        $limit = isset($_GET['limit']) ? (int) $_GET['limit'] : null;
        $offset = isset($_GET['offset']) ? (int) $_GET['offset'] : null;

        // Get transactions
        $transactions = $this->transactionModel->getUserTransactions($userId, $type, $status, $limit, $offset);

        // Return response
        ResponseHelper::success($transactions);
    }

    public function getTransaction($id) {
        // Authenticate user
        AuthMiddleware::authenticate();

        // Get user data
        $userId = $_REQUEST['user']->id;

        // Get transaction
        $transaction = $this->transactionModel->findById($id);

        if (!$transaction) {
            ResponseHelper::notFound('Transaction not found');
        }

        // Check if transaction belongs to user or user is admin
        if ($transaction['user_id'] !== $userId && $_REQUEST['user']->role !== 'admin') {
            ResponseHelper::forbidden('You do not have permission to view this transaction');
        }

        // Return response
        ResponseHelper::success($transaction);
    }

    public function createDeposit() {
        // Authenticate user
        AuthMiddleware::authenticate();

        // Get user data
        $userId = $_REQUEST['user']->id;
        $user = $this->userModel->findById($userId);

        if (!$user) {
            ResponseHelper::notFound('User not found');
        }

        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'amount' => 'required|numeric|minValue:10',
            'cryptocurrency' => 'required'
        ]);

        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }

        // Get deposit address from environment variables
        $cryptocurrency = strtoupper($data['cryptocurrency']);
        $depositAddress = $this->getDepositAddress($cryptocurrency);

        if (!$depositAddress) {
            ResponseHelper::error('Cryptocurrency not supported', 400);
        }

        // Create deposit transaction
        $transactionId = $this->transactionModel->createTransaction([
            'user_id' => $userId,
            'type' => 'deposit',
            'amount' => $data['amount'],
            'status' => 'pending',
            'wallet_address' => $depositAddress,
            'cryptocurrency' => $cryptocurrency,
            'description' => 'Deposit of ' . $data['amount'] . ' ' . $cryptocurrency
        ]);

        if (!$transactionId) {
            ResponseHelper::error('Failed to create deposit request', 500);
        }

        // Get created transaction
        $transaction = $this->transactionModel->findById($transactionId);

        // Return response with deposit address and expiry time
        ResponseHelper::success([
            'transaction' => $transaction,
            'deposit_address' => $depositAddress,
            'cryptocurrency' => $cryptocurrency,
            'amount' => $data['amount'],
            'expires_at' => date('Y-m-d H:i:s', time() + 1800), // 30 minutes from now
            'expires_in_seconds' => 1800 // 30 minutes
        ], 'Deposit address generated successfully', 201);
    }

    private function getDepositAddress($cryptocurrency) {
        $envKey = $cryptocurrency . '_DEPOSIT_ADDRESS';
        return $_ENV[$envKey] ?? null;
    }

    public function createWithdrawal() {
        // Authenticate user
        AuthMiddleware::authenticate();

        // Get user data
        $userId = $_REQUEST['user']->id;
        $user = $this->userModel->findById($userId);

        if (!$user) {
            ResponseHelper::notFound('User not found');
        }

        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);

        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'amount' => 'required|numeric|minValue:10',
            'wallet_address' => 'required'
        ]);

        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }

        // Check if user has enough balance
        if ($user['balance'] < $data['amount']) {
            ResponseHelper::error('Insufficient balance', 422);
        }

        // Calculate fee (if any)
        $fee = 0; // No fee for now

        // Create withdrawal transaction
        $transactionId = $this->transactionModel->createTransaction([
            'user_id' => $userId,
            'type' => 'withdrawal',
            'amount' => $data['amount'],
            'fee' => $fee,
            'status' => 'pending',
            'wallet_address' => $data['wallet_address'],
            'description' => 'Withdrawal of ' . $data['amount'] . ' USDT'
        ]);

        if (!$transactionId) {
            ResponseHelper::error('Failed to create withdrawal request', 500);
        }

        // Get created transaction
        $transaction = $this->transactionModel->findById($transactionId);

        // Return response
        ResponseHelper::success($transaction, 'Withdrawal request created successfully', 201);
    }

    public function approveTransaction($id) {
        // Authenticate admin
        AuthMiddleware::authenticateAdmin();

        // Get transaction
        $transaction = $this->transactionModel->findById($id);

        if (!$transaction) {
            ResponseHelper::notFound('Transaction not found');
        }

        // Check if transaction is pending
        if ($transaction['status'] !== 'pending') {
            ResponseHelper::error('Transaction is not pending', 400);
        }

        // Start database transaction
        $this->userModel->conn->beginTransaction();

        try {
            // Update transaction status
            if (!$this->transactionModel->updateStatus($id, 'completed')) {
                throw new \Exception('Failed to update transaction status');
            }

            // Update user balance if it's a deposit
            if ($transaction['type'] === 'deposit') {
                if (!$this->userModel->updateBalance($transaction['user_id'], $transaction['amount'])) {
                    throw new \Exception('Failed to update user balance');
                }
            }

            // Deduct from user balance if it's a withdrawal
            if ($transaction['type'] === 'withdrawal') {
                if (!$this->userModel->updateBalance($transaction['user_id'], -$transaction['amount'])) {
                    throw new \Exception('Failed to update user balance');
                }
            }

            // Commit transaction
            $this->userModel->conn->commit();

            // Return response
            ResponseHelper::success(null, 'Transaction approved successfully');
        } catch (\Exception $e) {
            // Rollback transaction
            $this->userModel->conn->rollBack();

            // Return error
            ResponseHelper::error($e->getMessage(), 500);
        }
    }

    public function rejectTransaction($id) {
        // Authenticate admin
        AuthMiddleware::authenticateAdmin();

        // Get transaction
        $transaction = $this->transactionModel->findById($id);

        if (!$transaction) {
            ResponseHelper::notFound('Transaction not found');
        }

        // Check if transaction is pending
        if ($transaction['status'] !== 'pending') {
            ResponseHelper::error('Transaction is not pending', 400);
        }

        // Update transaction status
        if (!$this->transactionModel->updateStatus($id, 'cancelled')) {
            ResponseHelper::error('Failed to update transaction status', 500);
        }

        // Return response
        ResponseHelper::success(null, 'Transaction rejected successfully');
    }
}
