<?php

namespace App\Controllers;

use App\Models\User;
use App\Models\Investment;
use App\Models\Transaction;
use App\Models\Earning;
use App\Helpers\ResponseHelper;
use App\Helpers\ValidationHelper;
use App\Middleware\AuthMiddleware;

class UserController {
    private $userModel;
    private $investmentModel;
    private $transactionModel;
    private $earningModel;
    
    public function __construct() {
        $this->userModel = new User();
        $this->investmentModel = new Investment();
        $this->transactionModel = new Transaction();
        $this->earningModel = new Earning();
    }
    
    public function getProfile() {
        // Authenticate user
        AuthMiddleware::authenticate();
        
        // Get user data
        $userId = $_REQUEST['user']->id;
        $user = $this->userModel->findById($userId);
        
        if (!$user) {
            ResponseHelper::notFound('User not found');
        }
        
        // Return response
        ResponseHelper::success([
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'profile_image' => $user['profile_image'],
            'balance' => $user['balance'],
            'is_verified' => (bool) $user['is_verified'],
            'role' => $user['role'],
            'status' => $user['status'],
            'last_login' => $user['last_login'],
            'created_at' => $user['created_at']
        ]);
    }
    
    public function updateProfile() {
        // Authenticate user
        AuthMiddleware::authenticate();
        
        // Get user data
        $userId = $_REQUEST['user']->id;
        $user = $this->userModel->findById($userId);
        
        if (!$user) {
            ResponseHelper::notFound('User not found');
        }
        
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'first_name' => 'required|max:50',
            'last_name' => 'required|max:50',
            'email' => 'required|email'
        ]);
        
        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }
        
        // Check if email is changed and already exists
        if ($data['email'] !== $user['email'] && $this->userModel->findByEmail($data['email'])) {
            ResponseHelper::error('Email already exists', 422);
        }
        
        // Update user
        $updateData = [
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email']
        ];
        
        if (!$this->userModel->update($userId, $updateData)) {
            ResponseHelper::error('Failed to update profile', 500);
        }
        
        // Get updated user
        $updatedUser = $this->userModel->findById($userId);
        
        // Return response
        ResponseHelper::success([
            'id' => $updatedUser['id'],
            'username' => $updatedUser['username'],
            'email' => $updatedUser['email'],
            'first_name' => $updatedUser['first_name'],
            'last_name' => $updatedUser['last_name'],
            'profile_image' => $updatedUser['profile_image'],
            'balance' => $updatedUser['balance'],
            'is_verified' => (bool) $updatedUser['is_verified'],
            'role' => $updatedUser['role'],
            'status' => $updatedUser['status'],
            'last_login' => $updatedUser['last_login'],
            'created_at' => $updatedUser['created_at']
        ], 'Profile updated successfully');
    }
    
    public function changePassword() {
        // Authenticate user
        AuthMiddleware::authenticate();
        
        // Get user data
        $userId = $_REQUEST['user']->id;
        $user = $this->userModel->findById($userId);
        
        if (!$user) {
            ResponseHelper::notFound('User not found');
        }
        
        // Get request data
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate input
        $validator = new ValidationHelper($data);
        $isValid = $validator->validate([
            'current_password' => 'required',
            'password' => 'required|min:8|confirmed'
        ]);
        
        if (!$isValid) {
            ResponseHelper::validation($validator->getErrors());
        }
        
        // Verify current password
        if (!password_verify($data['current_password'], $user['password'])) {
            ResponseHelper::error('Current password is incorrect', 422);
        }
        
        // Update password
        if (!$this->userModel->updatePassword($userId, $data['password'])) {
            ResponseHelper::error('Failed to update password', 500);
        }
        
        // Return response
        ResponseHelper::success(null, 'Password updated successfully');
    }
    
    public function getDashboard() {
        // Authenticate user
        AuthMiddleware::authenticate();
        
        // Get user data
        $userId = $_REQUEST['user']->id;
        $user = $this->userModel->findById($userId);
        
        if (!$user) {
            ResponseHelper::notFound('User not found');
        }
        
        // Get active investments
        $activeInvestments = $this->investmentModel->getUserInvestments($userId, 'active');
        
        // Get recent transactions
        $recentTransactions = $this->transactionModel->getUserTransactions($userId, null, null, 5);
        
        // Get total earnings
        $totalEarnings = $this->earningModel->getUserTotalEarnings($userId);
        
        // Calculate statistics
        $totalInvested = 0;
        $activeInvestmentCount = count($activeInvestments);
        
        foreach ($activeInvestments as $investment) {
            $totalInvested += $investment['amount'];
        }
        
        // Return response
        ResponseHelper::success([
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'balance' => $user['balance']
            ],
            'stats' => [
                'total_invested' => $totalInvested,
                'total_earnings' => $totalEarnings,
                'active_investments' => $activeInvestmentCount
            ],
            'active_investments' => $activeInvestments,
            'recent_transactions' => $recentTransactions
        ]);
    }
}
