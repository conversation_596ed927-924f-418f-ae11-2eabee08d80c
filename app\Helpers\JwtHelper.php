<?php

namespace App\Helpers;

use App\Config\App;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Exception;

class JwtHelper {
    private static $secret;
    private static $expiry;
    private static $algorithm = 'HS256';
    
    public static function init() {
        self::$secret = App::$jwtSecret;
        self::$expiry = App::$jwtExpiry;
    }
    
    public static function generateToken($userId, $username, $email, $role) {
        self::init();
        
        $issuedAt = time();
        $expiryTime = $issuedAt + self::$expiry;
        
        $payload = [
            'iss' => 'cryptoinvest',
            'aud' => 'cryptoinvest_users',
            'iat' => $issuedAt,
            'exp' => $expiryTime,
            'data' => [
                'id' => $userId,
                'username' => $username,
                'email' => $email,
                'role' => $role
            ]
        ];
        
        return JWT::encode($payload, self::$secret, self::$algorithm);
    }
    
    public static function validateToken($token) {
        self::init();
        
        try {
            $decoded = JWT::decode($token, new Key(self::$secret, self::$algorithm));
            return $decoded->data;
        } catch (Exception $e) {
            return false;
        }
    }
    
    public static function getAuthorizationHeader() {
        $headers = null;
        
        if (isset($_SERVER['Authorization'])) {
            $headers = trim($_SERVER['Authorization']);
        } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $headers = trim($_SERVER['HTTP_AUTHORIZATION']);
        } elseif (function_exists('apache_request_headers')) {
            $requestHeaders = apache_request_headers();
            $requestHeaders = array_combine(
                array_map('ucwords', array_keys($requestHeaders)),
                array_values($requestHeaders)
            );
            
            if (isset($requestHeaders['Authorization'])) {
                $headers = trim($requestHeaders['Authorization']);
            }
        }
        
        return $headers;
    }
    
    public static function getBearerToken() {
        $headers = self::getAuthorizationHeader();
        
        if (!empty($headers)) {
            if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }
}
