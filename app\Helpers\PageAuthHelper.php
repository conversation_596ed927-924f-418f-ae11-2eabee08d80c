<?php

namespace App\Helpers;

class PageAuthHelper {
    /**
     * Check if user is authenticated and redirect if not
     * Also sets cache control headers to prevent caching
     */
    public static function requireAuth() {
        // Set cache control headers to prevent caching
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Check if user is logged in (server-side authentication)
        if (!isset($_SESSION['user']) || !isset($_SESSION['token'])) {
            // User is not logged in, redirect to login page
            header('Location: ' . BASE_URL . '/login');
            exit;
        }

        // Validate token if it exists
        if (isset($_SESSION['token'])) {
            try {
                $userData = JwtHelper::validateToken($_SESSION['token']);

                if (!$userData) {
                    // Token is invalid, clear session and redirect
                    session_unset();
                    session_destroy();
                    header('Location: ' . BASE_URL . '/login');
                    exit;
                }

                // Update session user data if needed
                if (!isset($_SESSION['user'])) {
                    $_SESSION['user'] = $userData;
                }
            } catch (Exception $e) {
                // Token validation failed, clear session and redirect
                error_log('Token validation error in requireAuth: ' . $e->getMessage());
                session_unset();
                session_destroy();
                header('Location: ' . BASE_URL . '/login');
                exit;
            }
        }

        return true;
    }

    /**
     * Check if user is admin and redirect if not
     */
    public static function requireAdmin() {
        // First check basic authentication
        self::requireAuth();

        // Check if user is admin
        if (!isset($_SESSION['user']) || $_SESSION['user']->role !== 'admin') {
            // User is not admin, redirect to dashboard
            header('Location: ' . BASE_URL . '/dashboard');
            exit;
        }

        return true;
    }

    /**
     * Redirect authenticated users away from auth pages
     */
    public static function redirectIfAuthenticated() {
        // Set cache control headers
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Check if user is already logged in
        if (isset($_SESSION['user']) && isset($_SESSION['token'])) {
            try {
                // Validate token
                $userData = JwtHelper::validateToken($_SESSION['token']);

                if ($userData) {
                    // User is authenticated, redirect to dashboard
                    header('Location: ' . BASE_URL . '/dashboard');
                    exit;
                }
            } catch (Exception $e) {
                // Token validation failed, clear session
                error_log('Token validation error in redirectIfAuthenticated: ' . $e->getMessage());
            }

            // If we reach here, token is invalid, clear session
            session_unset();
            session_destroy();
        }

        return true;
    }
}
