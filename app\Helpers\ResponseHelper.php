<?php

namespace App\Helpers;

class ResponseHelper {
    public static function success($data = null, $message = 'Success', $statusCode = 200) {
        self::setHeaders($statusCode);
        
        echo json_encode([
            'status' => 'success',
            'message' => $message,
            'data' => $data
        ]);
        
        exit;
    }
    
    public static function error($message = 'An error occurred', $statusCode = 400, $errors = null) {
        self::setHeaders($statusCode);
        
        $response = [
            'status' => 'error',
            'message' => $message
        ];
        
        if ($errors !== null) {
            $response['errors'] = $errors;
        }
        
        echo json_encode($response);
        
        exit;
    }
    
    public static function notFound($message = 'Resource not found') {
        self::error($message, 404);
    }
    
    public static function unauthorized($message = 'Unauthorized access') {
        self::error($message, 401);
    }
    
    public static function forbidden($message = 'Access forbidden') {
        self::error($message, 403);
    }
    
    public static function validation($errors, $message = 'Validation failed') {
        self::error($message, 422, $errors);
    }
    
    private static function setHeaders($statusCode) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
    }
}
