<?php

namespace App\Helpers;

class ValidationHelper {
    private $errors = [];
    private $data = [];
    
    public function __construct($data) {
        $this->data = $data;
    }
    
    public function validate($rules) {
        foreach ($rules as $field => $fieldRules) {
            $fieldRules = explode('|', $fieldRules);
            
            foreach ($fieldRules as $rule) {
                if (strpos($rule, ':') !== false) {
                    list($ruleName, $ruleValue) = explode(':', $rule);
                } else {
                    $ruleName = $rule;
                    $ruleValue = null;
                }
                
                $methodName = 'validate' . ucfirst($ruleName);
                
                if (method_exists($this, $methodName)) {
                    $this->$methodName($field, $ruleValue);
                }
            }
        }
        
        return empty($this->errors);
    }
    
    public function getErrors() {
        return $this->errors;
    }
    
    private function validateRequired($field) {
        if (!isset($this->data[$field]) || empty(trim($this->data[$field]))) {
            $this->addError($field, 'The ' . $field . ' field is required');
        }
    }
    
    private function validateEmail($field) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_EMAIL)) {
                $this->addError($field, 'The ' . $field . ' must be a valid email address');
            }
        }
    }
    
    private function validateMin($field, $value) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (strlen($this->data[$field]) < $value) {
                $this->addError($field, 'The ' . $field . ' must be at least ' . $value . ' characters');
            }
        }
    }
    
    private function validateMax($field, $value) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (strlen($this->data[$field]) > $value) {
                $this->addError($field, 'The ' . $field . ' may not be greater than ' . $value . ' characters');
            }
        }
    }
    
    private function validateNumeric($field) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!is_numeric($this->data[$field])) {
                $this->addError($field, 'The ' . $field . ' must be a number');
            }
        }
    }
    
    private function validateAlpha($field) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!ctype_alpha($this->data[$field])) {
                $this->addError($field, 'The ' . $field . ' may only contain letters');
            }
        }
    }
    
    private function validateAlphaNum($field) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!ctype_alnum($this->data[$field])) {
                $this->addError($field, 'The ' . $field . ' may only contain letters and numbers');
            }
        }
    }
    
    private function validateConfirmed($field) {
        $confirmField = $field . '_confirmation';
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!isset($this->data[$confirmField]) || $this->data[$field] !== $this->data[$confirmField]) {
                $this->addError($field, 'The ' . $field . ' confirmation does not match');
            }
        }
    }
    
    private function validateMinValue($field, $value) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if ($this->data[$field] < $value) {
                $this->addError($field, 'The ' . $field . ' must be at least ' . $value);
            }
        }
    }
    
    private function validateMaxValue($field, $value) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if ($this->data[$field] > $value) {
                $this->addError($field, 'The ' . $field . ' may not be greater than ' . $value);
            }
        }
    }
    
    private function addError($field, $message) {
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        
        $this->errors[$field][] = $message;
    }
}
