<?php

namespace App\Middleware;

use App\Helpers\JwtHelper;

class AuthMiddleware {
    public static function authenticate() {
        // Check if token exists
        $token = JwtHelper::getBearerToken();

        if (!$token) {
            // Check for token in session
            if (isset($_SESSION['token'])) {
                $token = $_SESSION['token'];
            } else {
                self::sendUnauthorizedResponse();
                exit;
            }
        }

        // Validate token
        $userData = JwtHelper::validateToken($token);

        if (!$userData) {
            self::sendUnauthorizedResponse();
            exit;
        }

        // Set user data in request
        $_REQUEST['user'] = $userData;

        // Also set user data in session if not already set
        if (!isset($_SESSION['user'])) {
            $_SESSION['user'] = $userData;
        }

        return true;
    }

    public static function authenticateAdmin() {
        // First authenticate user
        self::authenticate();

        // Check if user is admin
        if ($_REQUEST['user']->role !== 'admin') {
            self::sendForbiddenResponse();
            exit;
        }

        return true;
    }

    private static function sendUnauthorizedResponse() {
        header('HTTP/1.1 401 Unauthorized');
        header('Content-Type: application/json');

        echo json_encode([
            'status' => 'error',
            'message' => 'Unauthorized access. Please login to continue.'
        ]);
    }

    private static function sendForbiddenResponse() {
        header('HTTP/1.1 403 Forbidden');
        header('Content-Type: application/json');

        echo json_encode([
            'status' => 'error',
            'message' => 'Access forbidden. You do not have permission to access this resource.'
        ]);
    }
}
