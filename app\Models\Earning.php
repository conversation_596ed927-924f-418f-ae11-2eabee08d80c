<?php

namespace App\Models;

use PDO;

class Earning extends BaseModel {
    protected $table = 'earnings';
    
    public function __construct() {
        parent::__construct();
    }
    
    public function getUserEarnings($userId, $status = null) {
        $sql = "
            SELECT e.*, i.amount as investment_amount, p.name as plan_name
            FROM {$this->table} e
            JOIN investments i ON e.investment_id = i.id
            JOIN investment_plans p ON i.plan_id = p.id
            WHERE e.user_id = :user_id
        ";
        
        if ($status !== null) {
            $sql .= " AND e.status = :status";
        }
        
        $sql .= " ORDER BY e.date DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindParam(':user_id', $userId);
        
        if ($status !== null) {
            $stmt->bindParam(':status', $status);
        }
        
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getInvestmentEarnings($investmentId) {
        $stmt = $this->conn->prepare("
            SELECT * FROM {$this->table} 
            WHERE investment_id = :investment_id
            ORDER BY date ASC
        ");
        $stmt->bindParam(':investment_id', $investmentId);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function createEarning($data) {
        return $this->create($data);
    }
    
    public function markAsPaid($id) {
        return $this->update($id, ['status' => 'paid']);
    }
    
    public function getPendingEarnings() {
        $stmt = $this->conn->prepare("
            SELECT e.*, u.username, i.amount as investment_amount, p.name as plan_name
            FROM {$this->table} e
            JOIN users u ON e.user_id = u.id
            JOIN investments i ON e.investment_id = i.id
            JOIN investment_plans p ON i.plan_id = p.id
            WHERE e.status = 'pending'
            ORDER BY e.date ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getTotalEarnings() {
        $stmt = $this->conn->prepare("
            SELECT SUM(amount) as total 
            FROM {$this->table}
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        return (float) $result['total'];
    }
    
    public function getUserTotalEarnings($userId) {
        $stmt = $this->conn->prepare("
            SELECT SUM(amount) as total 
            FROM {$this->table}
            WHERE user_id = :user_id
        ");
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        $result = $stmt->fetch();
        return (float) $result['total'];
    }
    
    public function calculateDailyEarnings() {
        // Get all active investments
        $investmentModel = new Investment();
        $activeInvestments = $investmentModel->getActiveInvestments();
        
        $today = date('Y-m-d');
        $earningsCreated = 0;
        
        foreach ($activeInvestments as $investment) {
            // Check if earnings for today already exist
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) as count 
                FROM {$this->table} 
                WHERE investment_id = :investment_id 
                AND date = :date
            ");
            $stmt->bindParam(':investment_id', $investment['id']);
            $stmt->bindParam(':date', $today);
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ((int) $result['count'] > 0) {
                continue; // Earnings already calculated for today
            }
            
            // Calculate daily earning
            $dailyAmount = $investment['amount'] * ($investment['daily_roi'] / 100);
            
            // Create earning record
            $earningData = [
                'user_id' => $investment['user_id'],
                'investment_id' => $investment['id'],
                'amount' => $dailyAmount,
                'date' => $today,
                'status' => 'pending'
            ];
            
            if ($this->createEarning($earningData)) {
                $earningsCreated++;
            }
        }
        
        return $earningsCreated;
    }
}
