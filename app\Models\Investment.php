<?php

namespace App\Models;

use PDO;

class Investment extends BaseModel {
    protected $table = 'investments';
    
    public function __construct() {
        parent::__construct();
    }
    
    public function getUserInvestments($userId, $status = null) {
        $sql = "
            SELECT i.*, p.name as plan_name, p.roi_percentage, p.duration_days, p.daily_roi
            FROM {$this->table} i
            JOIN investment_plans p ON i.plan_id = p.id
            WHERE i.user_id = :user_id
        ";
        
        if ($status !== null) {
            $sql .= " AND i.status = :status";
        }
        
        $sql .= " ORDER BY i.created_at DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindParam(':user_id', $userId);
        
        if ($status !== null) {
            $stmt->bindParam(':status', $status);
        }
        
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function createInvestment($data) {
        // Get plan details
        $planModel = new InvestmentPlan();
        $plan = $planModel->findById($data['plan_id']);
        
        if (!$plan) {
            return false;
        }
        
        // Calculate end date
        $startDate = new \DateTime($data['start_date'] ?? 'now');
        $endDate = clone $startDate;
        $endDate->modify("+{$plan['duration_days']} days");
        
        $data['end_date'] = $endDate->format('Y-m-d H:i:s');
        
        // Create investment
        return $this->create($data);
    }
    
    public function completeInvestment($id) {
        return $this->update($id, ['status' => 'completed']);
    }
    
    public function cancelInvestment($id) {
        return $this->update($id, ['status' => 'cancelled']);
    }
    
    public function getActiveInvestments() {
        $stmt = $this->conn->prepare("
            SELECT i.*, u.username, u.email, p.name as plan_name, p.roi_percentage, p.duration_days, p.daily_roi
            FROM {$this->table} i
            JOIN users u ON i.user_id = u.id
            JOIN investment_plans p ON i.plan_id = p.id
            WHERE i.status = 'active'
            ORDER BY i.created_at DESC
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getInvestmentsByDateRange($startDate, $endDate) {
        $stmt = $this->conn->prepare("
            SELECT i.*, u.username, p.name as plan_name
            FROM {$this->table} i
            JOIN users u ON i.user_id = u.id
            JOIN investment_plans p ON i.plan_id = p.id
            WHERE i.created_at BETWEEN :start_date AND :end_date
            ORDER BY i.created_at DESC
        ");
        $stmt->bindParam(':start_date', $startDate);
        $stmt->bindParam(':end_date', $endDate);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getTotalInvestmentAmount() {
        $stmt = $this->conn->prepare("
            SELECT SUM(amount) as total 
            FROM {$this->table}
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        return (float) $result['total'];
    }
    
    public function getInvestmentStats() {
        $stmt = $this->conn->prepare("
            SELECT 
                COUNT(*) as total_count,
                SUM(amount) as total_amount,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
                SUM(CASE WHEN status = 'active' THEN amount ELSE 0 END) as active_amount,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_amount
            FROM {$this->table}
        ");
        $stmt->execute();
        return $stmt->fetch();
    }
}
