<?php

namespace App\Models;

class InvestmentPlan extends BaseModel {
    protected $table = 'investment_plans';
    
    public function __construct() {
        parent::__construct();
    }
    
    public function getActivePlans() {
        $stmt = $this->conn->prepare("
            SELECT * FROM {$this->table} 
            WHERE status = 'active' 
            ORDER BY min_amount ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function createPlan($data) {
        // Calculate daily ROI if not provided
        if (!isset($data['daily_roi']) && isset($data['roi_percentage']) && isset($data['duration_days'])) {
            $data['daily_roi'] = $data['roi_percentage'] / $data['duration_days'];
        }
        
        return $this->create($data);
    }
    
    public function updatePlan($id, $data) {
        // Calculate daily ROI if not provided
        if (!isset($data['daily_roi']) && isset($data['roi_percentage']) && isset($data['duration_days'])) {
            $data['daily_roi'] = $data['roi_percentage'] / $data['duration_days'];
        }
        
        return $this->update($id, $data);
    }
    
    public function activatePlan($id) {
        return $this->update($id, ['status' => 'active']);
    }
    
    public function deactivatePlan($id) {
        return $this->update($id, ['status' => 'inactive']);
    }
}
