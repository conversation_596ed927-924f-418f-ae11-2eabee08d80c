<?php

namespace App\Models;

use PDO;

class Transaction extends BaseModel {
    protected $table = 'transactions';
    
    public function __construct() {
        parent::__construct();
    }
    
    public function getUserTransactions($userId, $type = null, $status = null, $limit = null, $offset = null) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = :user_id";
        
        if ($type !== null) {
            $sql .= " AND type = :type";
        }
        
        if ($status !== null) {
            $sql .= " AND status = :status";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        if ($limit !== null) {
            $sql .= " LIMIT :limit";
            if ($offset !== null) {
                $sql .= " OFFSET :offset";
            }
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindParam(':user_id', $userId);
        
        if ($type !== null) {
            $stmt->bindParam(':type', $type);
        }
        
        if ($status !== null) {
            $stmt->bindParam(':status', $status);
        }
        
        if ($limit !== null) {
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            if ($offset !== null) {
                $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            }
        }
        
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function createTransaction($data) {
        // Generate reference if not provided
        if (!isset($data['reference'])) {
            $data['reference'] = strtoupper(uniqid('TRX'));
        }
        
        return $this->create($data);
    }
    
    public function updateStatus($id, $status) {
        return $this->update($id, ['status' => $status]);
    }
    
    public function getPendingWithdrawals() {
        $stmt = $this->conn->prepare("
            SELECT t.*, u.username, u.email
            FROM {$this->table} t
            JOIN users u ON t.user_id = u.id
            WHERE t.type = 'withdrawal' AND t.status = 'pending'
            ORDER BY t.created_at ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getRecentTransactions($limit = 10) {
        $stmt = $this->conn->prepare("
            SELECT t.*, u.username
            FROM {$this->table} t
            JOIN users u ON t.user_id = u.id
            ORDER BY t.created_at DESC
            LIMIT :limit
        ");
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getTransactionsByDateRange($startDate, $endDate, $type = null) {
        $sql = "
            SELECT t.*, u.username
            FROM {$this->table} t
            JOIN users u ON t.user_id = u.id
            WHERE t.created_at BETWEEN :start_date AND :end_date
        ";
        
        if ($type !== null) {
            $sql .= " AND t.type = :type";
        }
        
        $sql .= " ORDER BY t.created_at DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindParam(':start_date', $startDate);
        $stmt->bindParam(':end_date', $endDate);
        
        if ($type !== null) {
            $stmt->bindParam(':type', $type);
        }
        
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getTransactionStats() {
        $stmt = $this->conn->prepare("
            SELECT 
                type,
                COUNT(*) as count,
                SUM(amount) as total_amount,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_amount
            FROM {$this->table}
            GROUP BY type
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }
}
