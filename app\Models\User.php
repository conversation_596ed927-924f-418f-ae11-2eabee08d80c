<?php

namespace App\Models;

use PDO;

class User extends BaseModel {
    protected $table = 'users';
    
    public function __construct() {
        parent::__construct();
    }
    
    public function findByEmail($email) {
        return $this->findOneBy('email', $email);
    }
    
    public function findByUsername($username) {
        return $this->findOneBy('username', $username);
    }
    
    public function findByVerificationToken($token) {
        return $this->findOneBy('verification_token', $token);
    }
    
    public function findByResetToken($token) {
        $stmt = $this->conn->prepare("
            SELECT * FROM {$this->table} 
            WHERE reset_token = :token 
            AND reset_token_expiry > NOW() 
            LIMIT 1
        ");
        $stmt->bindParam(':token', $token);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    public function createUser($data) {
        // Hash password
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        // Generate verification token
        if (!isset($data['verification_token'])) {
            $data['verification_token'] = bin2hex(random_bytes(32));
        }
        
        return $this->create($data);
    }
    
    public function verifyUser($userId) {
        return $this->update($userId, [
            'is_verified' => 1,
            'verification_token' => null
        ]);
    }
    
    public function updatePassword($userId, $password) {
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        return $this->update($userId, [
            'password' => $hashedPassword,
            'reset_token' => null,
            'reset_token_expiry' => null
        ]);
    }
    
    public function setResetToken($userId, $token, $expiry) {
        return $this->update($userId, [
            'reset_token' => $token,
            'reset_token_expiry' => $expiry
        ]);
    }
    
    public function updateLastLogin($userId) {
        return $this->update($userId, [
            'last_login' => date('Y-m-d H:i:s')
        ]);
    }
    
    public function updateBalance($userId, $amount) {
        $stmt = $this->conn->prepare("
            UPDATE {$this->table} 
            SET balance = balance + :amount 
            WHERE id = :id
        ");
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':id', $userId);
        return $stmt->execute();
    }
    
    public function getAllUsers($limit = null, $offset = null) {
        return $this->findAll($limit, $offset);
    }
    
    public function countActiveUsers() {
        $stmt = $this->conn->prepare("
            SELECT COUNT(*) as count 
            FROM {$this->table} 
            WHERE status = 'active'
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        return (int) $result['count'];
    }
}
