<?php

require_once __DIR__ . '/app/bootstrap.php';

use App\Config\Database;

// Initialize database connection
$database = new Database();
$conn = $database->connect();

// Check admin user
try {
    $stmt = $conn->query("SELECT id, username, email, role, is_verified, status FROM users WHERE role = 'admin'");
    echo "Admin users:\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "- ID: " . $row['id'] . "\n";
        echo "  Username: " . $row['username'] . "\n";
        echo "  Email: " . $row['email'] . "\n";
        echo "  Role: " . $row['role'] . "\n";
        echo "  Verified: " . ($row['is_verified'] ? 'Yes' : 'No') . "\n";
        echo "  Status: " . $row['status'] . "\n";
    }
    echo "\n";
} catch (PDOException $e) {
    echo "Error checking admin users: " . $e->getMessage() . "\n";
}

// Check investment plans
try {
    $stmt = $conn->query("SELECT id, name, min_amount, max_amount, roi_percentage, duration_days, daily_roi, status FROM investment_plans");
    echo "Investment plans:\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "- ID: " . $row['id'] . "\n";
        echo "  Name: " . $row['name'] . "\n";
        echo "  Min Amount: $" . $row['min_amount'] . "\n";
        echo "  Max Amount: $" . ($row['max_amount'] ? $row['max_amount'] : 'Unlimited') . "\n";
        echo "  ROI: " . $row['roi_percentage'] . "%\n";
        echo "  Duration: " . $row['duration_days'] . " days\n";
        echo "  Daily ROI: " . $row['daily_roi'] . "%\n";
        echo "  Status: " . $row['status'] . "\n";
    }
} catch (PDOException $e) {
    echo "Error checking investment plans: " . $e->getMessage() . "\n";
}
