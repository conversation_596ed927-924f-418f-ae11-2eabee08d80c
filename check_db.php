<?php

require_once __DIR__ . '/app/bootstrap.php';

use App\Config\Database;

// Initialize database connection
$database = new Database();
$conn = $database->connect();

// Check tables
try {
    $stmt = $conn->query('SHOW TABLES');
    echo "Tables in database:\n";
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        echo "- " . $row[0] . "\n";
    }
    echo "\n";
} catch (PDOException $e) {
    echo "Error listing tables: " . $e->getMessage() . "\n";
}

// Check users table structure
try {
    $stmt = $conn->query('DESCRIBE users');
    echo "Users table structure:\n";
    while ($row = $stmt->fetch()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
    }
    echo "\n";
} catch (PDOException $e) {
    echo "Error describing users table: " . $e->getMessage() . "\n";
}

// Check investment_plans table structure
try {
    $stmt = $conn->query('DESCRIBE investment_plans');
    echo "Investment plans table structure:\n";
    while ($row = $stmt->fetch()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
    }
    echo "\n";
} catch (PDOException $e) {
    echo "Error describing investment_plans table: " . $e->getMessage() . "\n";
}
