<?php

require_once __DIR__ . '/../app/bootstrap.php';

// Create database if it doesn't exist
try {
    $dbHost = $_ENV['DB_HOST'] ?? 'localhost';
    $dbName = $_ENV['DB_NAME'] ?? 'cryptoinvest';
    $dbUser = $_ENV['DB_USER'] ?? 'root';
    $dbPass = $_ENV['DB_PASS'] ?? '';

    $pdo = new PDO("mysql:host={$dbHost}", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}`");
    echo "Database created or already exists: {$dbName}" . PHP_EOL;

    // Select the database
    $pdo->exec("USE `{$dbName}`");

    // Use this connection for the rest of the script
    $conn = $pdo;
} catch (PDOException $e) {
    echo "Connection Error: " . $e->getMessage() . PHP_EOL;
    exit;
}

// Function to run SQL file
function runSqlFile($conn, $file) {
    try {
        $sql = file_get_contents($file);
        $conn->exec($sql);
        echo "Executed SQL file: " . basename($file) . PHP_EOL;
        return true;
    } catch (PDOException $e) {
        echo "Error executing SQL file " . basename($file) . ": " . $e->getMessage() . PHP_EOL;
        return false;
    }
}

// Drop existing tables if they exist
try {
    $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
    $tables = ['earnings', 'transactions', 'investments', 'investment_plans', 'users'];
    foreach ($tables as $table) {
        $conn->exec("DROP TABLE IF EXISTS `{$table}`");
        echo "Dropped table if exists: {$table}" . PHP_EOL;
    }
    $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
} catch (PDOException $e) {
    echo "Error dropping tables: " . $e->getMessage() . PHP_EOL;
}

// Get all migration files
$migrationFiles = glob(__DIR__ . '/migrations/*.sql');
sort($migrationFiles); // Sort to ensure correct order

// Run each migration file
foreach ($migrationFiles as $file) {
    runSqlFile($conn, $file);
}

// Insert default admin user
try {
    $password = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $conn->prepare("
        INSERT INTO users (username, email, password, role, is_verified, status)
        VALUES ('admin', '<EMAIL>', :password, 'admin', 1, 'active')
    ");
    $stmt->bindParam(':password', $password);
    $stmt->execute();
    echo "Default admin user created" . PHP_EOL;
} catch (PDOException $e) {
    echo "Error creating default admin user: " . $e->getMessage() . PHP_EOL;
}

// Insert default investment plan
try {
    $stmt = $conn->prepare("
        INSERT INTO investment_plans (name, description, min_amount, max_amount, roi_percentage, duration_days, daily_roi, status)
        VALUES ('USDT Starter Plan', 'Start investing with our beginner-friendly USDT plan with 5% APY', 100.00, 1000.00, 5.00, 30, 0.16, 'active')
    ");
    $stmt->execute();
    echo "Default investment plan created" . PHP_EOL;
} catch (PDOException $e) {
    echo "Error creating default investment plan: " . $e->getMessage() . PHP_EOL;
}

echo "Database initialization completed" . PHP_EOL;
