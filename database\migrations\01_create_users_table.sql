CREATE TABLE IF NOT EXISTS `users` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `username` VA<PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE,
    `email` VARCHAR(100) NOT NULL UNIQUE,
    `password` VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    `first_name` <PERSON><PERSON><PERSON><PERSON>(50),
    `last_name` VA<PERSON><PERSON><PERSON>(50),
    `profile_image` VARCHAR(255) DEFAULT NULL,
    `balance` DECIMAL(15, 2) DEFAULT 0.00,
    `is_verified` TINYINT(1) DEFAULT 0,
    `verification_token` VARCHAR(100) DEFAULT NULL,
    `reset_token` VARCHAR(100) DEFAULT NULL,
    `reset_token_expiry` DATETIME DEFAULT NULL,
    `role` ENUM('user', 'admin') DEFAULT 'user',
    `status` ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    `last_login` DATETIME DEFAULT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
