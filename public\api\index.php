<?php

// Load bootstrap file
require_once __DIR__ . '/../../app/bootstrap.php';

// Set headers for API
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Access-Control-Allow-Headers, Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Get request method and URI
$method = $_SERVER['REQUEST_METHOD'];
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove base path from URI
$basePath = '/api';
if (strpos($uri, $basePath) === 0) {
    $uri = substr($uri, strlen($basePath));
}

// Initialize controllers
$authController = new App\Controllers\AuthController();
$userController = new App\Controllers\UserController();
$investmentController = new App\Controllers\InvestmentController();
$transactionController = new App\Controllers\TransactionController();
$adminController = new App\Controllers\AdminController();

// Define routes
$routes = [
    // Auth routes
    'POST /auth/register' => [$authController, 'register'],
    'POST /auth/login' => [$authController, 'login'],
    'POST /auth/logout' => [$authController, 'logout'],
    'GET /auth/verify' => [$authController, 'verifyEmail'],
    'POST /auth/forgot-password' => [$authController, 'forgotPassword'],
    'POST /auth/reset-password' => [$authController, 'resetPassword'],
    
    // User routes
    'GET /user/profile' => [$userController, 'getProfile'],
    'PUT /user/profile' => [$userController, 'updateProfile'],
    'POST /user/change-password' => [$userController, 'changePassword'],
    'GET /user/dashboard' => [$userController, 'getDashboard'],
    
    // Investment routes
    'GET /investments/plans' => [$investmentController, 'getPlans'],
    'GET /investments/plans/{id}' => [$investmentController, 'getPlan'],
    'GET /investments' => [$investmentController, 'getUserInvestments'],
    'GET /investments/{id}' => [$investmentController, 'getInvestment'],
    'POST /investments' => [$investmentController, 'createInvestment'],
    'POST /investments/calculate-earnings' => [$investmentController, 'calculateEarnings'],
    
    // Transaction routes
    'GET /transactions' => [$transactionController, 'getUserTransactions'],
    'GET /transactions/{id}' => [$transactionController, 'getTransaction'],
    'POST /transactions/deposit' => [$transactionController, 'createDeposit'],
    'POST /transactions/withdrawal' => [$transactionController, 'createWithdrawal'],
    'PUT /transactions/{id}/approve' => [$transactionController, 'approveTransaction'],
    'PUT /transactions/{id}/reject' => [$transactionController, 'rejectTransaction'],
    
    // Admin routes
    'GET /admin/dashboard' => [$adminController, 'getDashboard'],
    'GET /admin/users' => [$adminController, 'getUsers'],
    'GET /admin/users/{id}' => [$adminController, 'getUser'],
    'PUT /admin/users/{id}' => [$adminController, 'updateUser'],
    'POST /admin/plans' => [$adminController, 'createPlan'],
    'PUT /admin/plans/{id}' => [$adminController, 'updatePlan'],
    'DELETE /admin/plans/{id}' => [$adminController, 'deletePlan'],
    'GET /admin/withdrawals/pending' => [$adminController, 'getPendingWithdrawals']
];

// Match route
$routeFound = false;

foreach ($routes as $route => $handler) {
    // Split route into method and path
    list($routeMethod, $routePath) = explode(' ', $route);
    
    // Check if method matches
    if ($routeMethod !== $method) {
        continue;
    }
    
    // Convert route path to regex pattern
    $pattern = preg_replace('/\{([a-zA-Z0-9_]+)\}/', '(?P<$1>[^/]+)', $routePath);
    $pattern = '#^' . $pattern . '$#';
    
    // Match URI against pattern
    if (preg_match($pattern, $uri, $matches)) {
        $routeFound = true;
        
        // Extract parameters
        $params = array_filter($matches, function($key) {
            return !is_numeric($key);
        }, ARRAY_FILTER_USE_KEY);
        
        // Call handler with parameters
        call_user_func_array($handler, $params);
        break;
    }
}

// If no route found, return 404
if (!$routeFound) {
    http_response_code(404);
    echo json_encode([
        'status' => 'error',
        'message' => 'Endpoint not found'
    ]);
}
