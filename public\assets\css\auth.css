/* Auth Section */
.auth-section {
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    padding: 3rem 0;
    background-color: #f8f9fa;
}

.auth-section .card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.auth-section .card-body {
    padding: 2.5rem;
}

.auth-section h2 {
    font-weight: 700;
    color: #2c3e50;
}

.auth-section .form-label {
    font-weight: 500;
    color: #7f8c8d;
}

.auth-section .input-group-text {
    background-color: #f8f9fa;
    border-right: none;
}

.auth-section .form-control {
    border-left: none;
}

.auth-section .form-control:focus {
    box-shadow: none;
    border-color: #ced4da;
}

.auth-section .input-group:focus-within .input-group-text,
.auth-section .input-group:focus-within .form-control {
    border-color: #3498db;
}

.auth-section .toggle-password {
    border-left: none;
    background-color: #f8f9fa;
}

.auth-section .toggle-password:hover {
    background-color: #e9ecef;
}

.auth-section .btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
}

.auth-section .btn-primary:hover {
    background-color: #217dbb;
    border-color: #217dbb;
}

.auth-section a {
    color: #3498db;
    text-decoration: none;
    transition: all 0.3s ease;
}

.auth-section a:hover {
    color: #217dbb;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .auth-section {
        padding: 2rem 0;
    }
    
    .auth-section .card-body {
        padding: 1.5rem;
    }
}
