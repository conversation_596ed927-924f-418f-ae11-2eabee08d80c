/* Dashboard Section */
.dashboard-section {
    padding: 2rem 0;
    background-color: #f8f9fa;
    min-height: calc(100vh - 200px);
}

/* Sidebar */
.avatar-container {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    position: relative;
}

.avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border: 3px solid #3498db;
}

.list-group-item {
    border: none;
    padding: 0.75rem 1.25rem;
    font-weight: 500;
}

.list-group-item.active {
    background-color: #3498db;
    border-color: #3498db;
}

.list-group-item:not(.active):hover {
    background-color: #f8f9fa;
    color: #3498db;
}

/* Balance Section */
.balance-section {
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Dashboard Menu Styles */
.dashboard-menu {
    padding: 0;
}

.menu-section {
    margin-bottom: 0.5rem;
}

.menu-section:last-child {
    margin-bottom: 0;
    border-top: 1px solid #e9ecef;
    padding-top: 0.5rem;
}

.menu-section-title {
    padding: 0.75rem 1.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    color: #495057;
    text-decoration: none;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
}

.menu-item:hover {
    background-color: #f8f9fa;
    color: #3498db;
    text-decoration: none;
    transform: translateX(2px);
}

.menu-item.active {
    background-color: #3498db;
    color: white;
    border-left: 4px solid #2980b9;
}

.menu-item.active:hover {
    background-color: #2980b9;
    color: white;
    transform: translateX(0);
}

.menu-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    border-radius: 8px;
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
    transition: all 0.3s ease;
}

.menu-item:hover .menu-icon {
    background-color: rgba(52, 152, 219, 0.2);
    transform: scale(1.05);
}

.menu-item.active .menu-icon {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.menu-text {
    flex: 1;
    font-size: 0.95rem;
}

.menu-badge {
    margin-left: auto;
    opacity: 0;
    transition: all 0.3s ease;
    color: #3498db;
}

.menu-item:hover .menu-badge {
    opacity: 1;
    transform: translateX(2px);
}

/* Special Menu Item Styles */
.menu-item.deposit-action {
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    border-left: 4px solid #28a745;
    color: #155724;
}

.menu-item.deposit-action:hover {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    transform: translateX(4px);
}

.menu-item.deposit-action .menu-icon {
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.menu-item.deposit-action:hover .menu-icon {
    background-color: rgba(40, 167, 69, 0.3);
    color: #1e7e34;
}

.menu-item.deposit-action .menu-badge {
    color: #28a745;
    opacity: 1;
}

.menu-item.logout-action {
    color: #dc3545;
    border-top: 1px solid #f8d7da;
}

.menu-item.logout-action:hover {
    background-color: #f8d7da;
    color: #721c24;
}

.menu-item.logout-action .menu-icon {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.menu-item.logout-action:hover .menu-icon {
    background-color: rgba(220, 53, 69, 0.2);
    color: #721c24;
}

/* Responsive Menu Adjustments */
@media (max-width: 992px) {
    .menu-item {
        padding: 0.875rem 1rem;
    }

    .menu-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.5rem;
    }

    .menu-text {
        font-size: 0.9rem;
    }

    .menu-section-title {
        padding: 0.625rem 1rem 0.375rem;
        font-size: 0.7rem;
    }
}

/* Stats Cards */
.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.bg-primary-light {
    background-color: rgba(52, 152, 219, 0.1);
}

.bg-success-light {
    background-color: rgba(46, 204, 113, 0.1);
}

.bg-info-light {
    background-color: rgba(52, 152, 219, 0.1);
}

.bg-warning-light {
    background-color: rgba(243, 156, 18, 0.1);
}

.bg-danger-light {
    background-color: rgba(231, 76, 60, 0.1);
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* Status Badges */
.badge {
    padding: 0.5rem 0.75rem;
    font-weight: 500;
    border-radius: 4px;
}

.badge-success {
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
}

.badge-warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.badge-danger {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.badge-info {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

/* Transaction Types */
.transaction-type {
    display: inline-flex;
    align-items: center;
    font-weight: 500;
}

.transaction-type i {
    margin-right: 0.5rem;
}

.transaction-deposit {
    color: #2ecc71;
}

.transaction-withdrawal {
    color: #e74c3c;
}

.transaction-investment {
    color: #3498db;
}

.transaction-earning {
    color: #f39c12;
}

/* Dashboard Footer Styles */
.dashboard-footer {
    background-color: #ffffff !important;
    border-top: 1px solid #e9ecef !important;
    padding: 1rem 0 !important;
    margin-top: 2rem;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.dashboard-footer .text-muted {
    color: #6c757d !important;
    font-size: 0.875rem;
}

.dashboard-footer-links a {
    text-decoration: none;
    transition: color 0.2s ease;
    font-weight: 500;
}

.dashboard-footer-links a:hover {
    color: #3498db !important;
    text-decoration: none;
}

.dashboard-footer-links i {
    font-size: 0.75rem;
    opacity: 0.8;
}

.dashboard-footer .text-success {
    color: #28a745 !important;
}

/* Responsive Footer Adjustments */
@media (max-width: 768px) {
    .dashboard-footer-links {
        margin-top: 0.5rem;
    }

    .dashboard-footer-links a {
        display: block;
        margin-bottom: 0.25rem;
        margin-right: 0 !important;
    }

    .dashboard-footer .col-md-6:first-child {
        margin-bottom: 0.5rem;
    }
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .dashboard-section {
        padding: 1.5rem 0;
    }
}
