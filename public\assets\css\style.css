/* Global Styles */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --body-bg: #f8f9fa;
    --card-bg: #ffffff;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--body-bg);
    color: #333;
    line-height: 1.6;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: #217dbb;
}

.btn {
    border-radius: 4px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #217dbb;
    border-color: #217dbb;
}

.card {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Header Styles */
.navbar {
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #ffffff;
}

/* Footer Styles */
footer {
    background-color: var(--dark-color);
    color: #ffffff;
}

footer a {
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

footer a:hover {
    color: #ffffff;
    text-decoration: none;
}

/* Utility Classes */
.bg-primary-light {
    background-color: rgba(52, 152, 219, 0.1);
}

.bg-success-light {
    background-color: rgba(46, 204, 113, 0.1);
}

.bg-info-light {
    background-color: rgba(52, 152, 219, 0.1);
}

.bg-warning-light {
    background-color: rgba(243, 156, 18, 0.1);
}

.bg-danger-light {
    background-color: rgba(231, 76, 60, 0.1);
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem 0;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .btn {
        padding: 0.4rem 1.2rem;
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}
