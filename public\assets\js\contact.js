// Contact JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    const submitButton = document.getElementById('submit-button');
    const submitSpinner = document.getElementById('submit-spinner');
    const successAlert = document.getElementById('contact-success');
    const errorAlert = document.getElementById('contact-error');
    
    if (contactForm) {
        contactForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Clear previous alerts
            successAlert.classList.add('d-none');
            errorAlert.classList.add('d-none');
            
            // Show loading state
            submitButton.disabled = true;
            submitSpinner.classList.remove('d-none');
            
            // Get form data
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                subject: document.getElementById('subject').value,
                message: document.getElementById('message').value
            };
            
            // In a real application, this would send the form data to the server
            // For demo purposes, we'll simulate a successful submission
            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Show success message
                successAlert.classList.remove('d-none');
                
                // Reset form
                contactForm.reset();
            } catch (error) {
                // Show error message
                errorAlert.textContent = error.message || 'There was an error sending your message. Please try again.';
                errorAlert.classList.remove('d-none');
            } finally {
                // Reset loading state
                submitButton.disabled = false;
                submitSpinner.classList.add('d-none');
            }
        });
    }
});
