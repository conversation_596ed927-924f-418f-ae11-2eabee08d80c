// Dashboard JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    // Remove client-side redirect check - let server handle authentication
    // Server-side authentication is more reliable and prevents redirect loops

    // Load dashboard data
    loadDashboardData();
});

// Load dashboard data
async function loadDashboardData() {
    try {
        // Get dashboard data
        const response = await apiRequest('/user/dashboard');
        const data = response.data;

        // Update user info
        updateUserInfo(data.user);

        // Update statistics
        updateStatistics(data.stats);

        // Update active investments
        updateActiveInvestments(data.active_investments);

        // Update recent transactions
        updateRecentTransactions(data.recent_transactions);
    } catch (error) {
        console.error('Error loading dashboard data:', error);

        // Check if unauthorized
        if (error.message === 'Unauthorized access. Please login to continue.') {
            logout();
        }
    }
}

// Update user information
function updateUserInfo(user) {
    const userNameElement = document.getElementById('user-name');
    const userEmailElement = document.getElementById('user-email');
    const userBalanceElement = document.getElementById('user-balance');

    if (userNameElement) {
        userNameElement.textContent = user.username;
    }

    if (userEmailElement) {
        userEmailElement.textContent = user.email;
    }

    if (userBalanceElement) {
        userBalanceElement.textContent = formatCurrency(user.balance);
    }
}

// Update statistics
function updateStatistics(stats) {
    const totalInvestedElement = document.getElementById('total-invested');
    const totalEarningsElement = document.getElementById('total-earnings');
    const activeInvestmentsElement = document.getElementById('active-investments');

    const investedProgressElement = document.getElementById('invested-progress');
    const earningsProgressElement = document.getElementById('earnings-progress');
    const activeProgressElement = document.getElementById('active-progress');

    if (totalInvestedElement) {
        totalInvestedElement.textContent = formatCurrency(stats.total_invested);
    }

    if (totalEarningsElement) {
        totalEarningsElement.textContent = formatCurrency(stats.total_earnings);
    }

    if (activeInvestmentsElement) {
        activeInvestmentsElement.textContent = stats.active_investments;
    }

    // Update progress bars
    if (investedProgressElement) {
        const investedPercentage = Math.min(stats.total_invested / 1000 * 100, 100);
        investedProgressElement.style.width = `${investedPercentage}%`;
    }

    if (earningsProgressElement) {
        const earningsPercentage = Math.min(stats.total_earnings / 500 * 100, 100);
        earningsProgressElement.style.width = `${earningsPercentage}%`;
    }

    if (activeProgressElement) {
        const activePercentage = Math.min(stats.active_investments / 5 * 100, 100);
        activeProgressElement.style.width = `${activePercentage}%`;
    }
}

// Update active investments
function updateActiveInvestments(investments) {
    const activeInvestmentsTable = document.getElementById('active-investments-table');

    if (activeInvestmentsTable) {
        if (investments.length === 0) {
            activeInvestmentsTable.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4">No active investments found</td>
                </tr>
            `;
        } else {
            let html = '';

            investments.forEach(investment => {
                html += `
                    <tr>
                        <td>${investment.plan_name}</td>
                        <td>${formatCurrency(investment.amount)}</td>
                        <td>${investment.roi_percentage}%</td>
                        <td>${formatDate(investment.start_date)}</td>
                        <td>${formatDate(investment.end_date)}</td>
                        <td>${getStatusBadge(investment.status)}</td>
                    </tr>
                `;
            });

            activeInvestmentsTable.innerHTML = html;
        }
    }
}

// Update recent transactions
function updateRecentTransactions(transactions) {
    const recentTransactionsTable = document.getElementById('recent-transactions-table');

    if (recentTransactionsTable) {
        if (transactions.length === 0) {
            recentTransactionsTable.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center py-4">No transactions found</td>
                </tr>
            `;
        } else {
            let html = '';

            transactions.forEach(transaction => {
                html += `
                    <tr>
                        <td>${getTransactionTypeBadge(transaction.type)}</td>
                        <td>${formatCurrency(transaction.amount)}</td>
                        <td>${getStatusBadge(transaction.status)}</td>
                        <td>${formatDate(transaction.created_at)}</td>
                    </tr>
                `;
            });

            recentTransactionsTable.innerHTML = html;
        }
    }
}
