// Deposit JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    // Load user data for sidebar
    loadUserData();

    // Load recent deposits
    loadRecentDeposits();

    // Setup form handler
    setupDepositForm();
});

// Cleanup timer when page is unloaded
window.addEventListener('beforeunload', function() {
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }
});

// Load user data for sidebar
async function loadUserData() {
    try {
        const response = await apiRequest('/user/dashboard');
        const data = response.data;

        // Update user info in sidebar
        updateUserInfo(data.user);
        updateUserBalance(data.user.balance);
    } catch (error) {
        console.error('Error loading user data:', error);

        // Check if unauthorized
        if (error.message === 'Unauthorized access. Please login to continue.') {
            logout();
        }
    }
}

// Update user info in sidebar
function updateUserInfo(user) {
    const userNameElement = document.getElementById('user-name');
    const userEmailElement = document.getElementById('user-email');

    if (userNameElement) {
        userNameElement.textContent = user.username || 'User';
    }

    if (userEmailElement) {
        userEmailElement.textContent = user.email || '';
    }
}

// Update user balance in sidebar
function updateUserBalance(balance) {
    const balanceElement = document.getElementById('user-balance');
    if (balanceElement) {
        balanceElement.textContent = formatCurrency(balance || 0);
    }
}

// Setup deposit form
function setupDepositForm() {
    const form = document.getElementById('deposit-form');
    if (!form) return;

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        const amount = formData.get('amount');
        const cryptocurrency = formData.get('cryptocurrency');

        // Validate form
        if (!amount || !cryptocurrency) {
            showAlert('Please fill in all required fields.', 'warning');
            return;
        }

        if (parseFloat(amount) < 10) {
            showAlert('Minimum deposit amount is $10.00', 'warning');
            return;
        }

        try {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Generating...';
            submitBtn.disabled = true;

            // Generate deposit address (using existing deposit endpoint)
            const response = await apiRequest('/transactions/deposit', 'POST', {
                amount: parseFloat(amount),
                cryptocurrency: cryptocurrency
            });

            if (response.success) {
                // Show deposit address with countdown timer
                showDepositAddress(
                    response.data.deposit_address,
                    cryptocurrency,
                    amount,
                    response.data.expires_in_seconds
                );
                showAlert('Deposit address generated successfully!', 'success');
            } else {
                throw new Error(response.message || 'Failed to generate deposit address');
            }
        } catch (error) {
            console.error('Error generating deposit address:', error);
            showAlert(error.message || 'Failed to generate deposit address. Please try again.', 'error');
        } finally {
            // Reset button
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });
}

// Show deposit address with countdown timer
function showDepositAddress(address, cryptocurrency, amount, expiresInSeconds = 1800) {
    const section = document.getElementById('deposit-address-section');
    const addressElement = document.getElementById('deposit-address');
    const cryptoElements = document.querySelectorAll('#selected-crypto, #selected-crypto-2');

    if (section && addressElement) {
        addressElement.textContent = address;

        cryptoElements.forEach(element => {
            element.textContent = cryptocurrency;
        });

        // Start countdown timer
        startCountdownTimer(expiresInSeconds);

        section.style.display = 'block';

        // Scroll to the address section
        section.scrollIntoView({ behavior: 'smooth' });
    }
}

// Countdown timer variables
let countdownInterval = null;

// Start countdown timer
function startCountdownTimer(seconds) {
    // Clear any existing timer
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }

    let timeLeft = seconds;
    const countdownDisplay = document.getElementById('countdown-display');
    const timerContainer = countdownDisplay?.closest('.bg-warning');

    if (!countdownDisplay) return;

    // Update timer immediately
    updateCountdownDisplay(timeLeft, countdownDisplay);

    // Start the countdown
    countdownInterval = setInterval(() => {
        timeLeft--;

        if (timeLeft <= 0) {
            // Timer expired
            clearInterval(countdownInterval);
            handleTimerExpiry(timerContainer);
        } else {
            updateCountdownDisplay(timeLeft, countdownDisplay);

            // Change color when less than 5 minutes remaining
            if (timeLeft <= 300) { // 5 minutes
                countdownDisplay.className = 'badge bg-danger text-white fs-6';
                if (timerContainer) {
                    timerContainer.className = timerContainer.className.replace('bg-warning', 'bg-danger');
                    timerContainer.className = timerContainer.className.replace('border-warning', 'border-danger');
                }
            }
        }
    }, 1000);
}

// Update countdown display
function updateCountdownDisplay(seconds, displayElement) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    const timeString = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    displayElement.textContent = timeString;
}

// Handle timer expiry
function handleTimerExpiry(timerContainer) {
    const countdownDisplay = document.getElementById('countdown-display');

    if (countdownDisplay) {
        countdownDisplay.textContent = 'EXPIRED';
        countdownDisplay.className = 'badge bg-danger text-white fs-6';
    }

    if (timerContainer) {
        timerContainer.className = 'mb-3 p-3 bg-danger bg-opacity-10 border border-danger rounded';
        timerContainer.innerHTML = `
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    <strong>Deposit time has expired!</strong>
                </div>
                <div>
                    <span class="badge bg-danger text-white fs-6">EXPIRED</span>
                </div>
            </div>
            <small class="text-muted">
                Please generate a new deposit address to continue with your deposit.
            </small>
        `;
    }

    // Show alert
    showAlert('Deposit time has expired! Please generate a new address to continue.', 'warning');
}

// Generate mock address for demonstration (fallback)
function generateMockAddress(cryptocurrency) {
    const addresses = {
        'USDT': 'TQn9Y2khEsLJW1ChVWFMSMeRDow5CNYY7r',
        'BTC': '**********************************',
        'ETH': '******************************************'
    };

    return addresses[cryptocurrency] || addresses['USDT'];
}

// Copy to clipboard function
function copyToClipboard() {
    const addressElement = document.getElementById('deposit-address');
    if (addressElement) {
        const address = addressElement.textContent;

        // Use modern clipboard API if available
        if (navigator.clipboard) {
            navigator.clipboard.writeText(address).then(() => {
                showAlert('Address copied to clipboard!', 'success');
            }).catch(() => {
                fallbackCopyToClipboard(address);
            });
        } else {
            fallbackCopyToClipboard(address);
        }
    }
}

// Fallback copy to clipboard for older browsers
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showAlert('Address copied to clipboard!', 'success');
    } catch (err) {
        console.error('Failed to copy to clipboard:', err);
        showAlert('Failed to copy address. Please copy manually.', 'warning');
    }

    document.body.removeChild(textArea);
}

// Load recent deposits
async function loadRecentDeposits() {
    try {
        // Use existing transactions endpoint with deposit filter
        const response = await apiRequest('/transactions?type=deposit&limit=5');

        if (response.success && response.data) {
            displayRecentDeposits(response.data);
        }
    } catch (error) {
        console.error('Error loading recent deposits:', error);

        const container = document.getElementById('recent-deposits');
        if (container) {
            container.innerHTML = '<p class="text-muted text-center">No recent deposits found.</p>';
        }
    }
}

// Display recent deposits
function displayRecentDeposits(deposits) {
    const container = document.getElementById('recent-deposits');
    if (!container) return;

    if (!deposits || deposits.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No recent deposits found.</p>';
        return;
    }

    const html = deposits.map(deposit => `
        <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
            <div>
                <small class="text-muted">${formatDate(deposit.created_at)}</small>
                <div class="fw-bold">${formatCurrency(deposit.amount)}</div>
            </div>
            <span class="badge bg-${getStatusColor(deposit.status)}">${deposit.status}</span>
        </div>
    `).join('');

    container.innerHTML = html;
}

// Get status color for badges
function getStatusColor(status) {
    switch (status.toLowerCase()) {
        case 'completed':
        case 'confirmed':
            return 'success';
        case 'pending':
            return 'warning';
        case 'failed':
        case 'cancelled':
            return 'danger';
        default:
            return 'secondary';
    }
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount || 0);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Show alert message
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at the top of the main content
    const mainContent = document.querySelector('.col-lg-9');
    if (mainContent) {
        mainContent.insertBefore(alertDiv, mainContent.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}
