// Deposit JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    // Load user data for sidebar
    loadUserData();
    
    // Load recent deposits
    loadRecentDeposits();
    
    // Setup form handler
    setupDepositForm();
});

// Load user data for sidebar
async function loadUserData() {
    try {
        const response = await apiRequest('/user/dashboard');
        const data = response.data;
        
        // Update user info in sidebar
        updateUserInfo(data.user);
        updateUserBalance(data.user.balance);
    } catch (error) {
        console.error('Error loading user data:', error);
        
        // Check if unauthorized
        if (error.message === 'Unauthorized access. Please login to continue.') {
            logout();
        }
    }
}

// Update user info in sidebar
function updateUserInfo(user) {
    const userNameElement = document.getElementById('user-name');
    const userEmailElement = document.getElementById('user-email');
    
    if (userNameElement) {
        userNameElement.textContent = user.username || 'User';
    }
    
    if (userEmailElement) {
        userEmailElement.textContent = user.email || '';
    }
}

// Update user balance in sidebar
function updateUserBalance(balance) {
    const balanceElement = document.getElementById('user-balance');
    if (balanceElement) {
        balanceElement.textContent = formatCurrency(balance || 0);
    }
}

// Setup deposit form
function setupDepositForm() {
    const form = document.getElementById('deposit-form');
    if (!form) return;
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const amount = formData.get('amount');
        const cryptocurrency = formData.get('cryptocurrency');
        
        // Validate form
        if (!amount || !cryptocurrency) {
            showAlert('Please fill in all required fields.', 'warning');
            return;
        }
        
        if (parseFloat(amount) < 10) {
            showAlert('Minimum deposit amount is $10.00', 'warning');
            return;
        }
        
        try {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Generating...';
            submitBtn.disabled = true;
            
            // Generate deposit address
            const response = await apiRequest('/user/deposit/generate-address', {
                method: 'POST',
                body: JSON.stringify({
                    amount: parseFloat(amount),
                    cryptocurrency: cryptocurrency
                })
            });
            
            if (response.success) {
                // Show deposit address
                showDepositAddress(response.data.address, cryptocurrency, amount);
                showAlert('Deposit address generated successfully!', 'success');
            } else {
                throw new Error(response.message || 'Failed to generate deposit address');
            }
        } catch (error) {
            console.error('Error generating deposit address:', error);
            showAlert(error.message || 'Failed to generate deposit address. Please try again.', 'error');
        } finally {
            // Reset button
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });
}

// Show deposit address
function showDepositAddress(address, cryptocurrency, amount) {
    const section = document.getElementById('deposit-address-section');
    const addressElement = document.getElementById('deposit-address');
    const cryptoElements = document.querySelectorAll('#selected-crypto, #selected-crypto-2');
    
    if (section && addressElement) {
        addressElement.textContent = address;
        
        cryptoElements.forEach(element => {
            element.textContent = cryptocurrency;
        });
        
        section.style.display = 'block';
        
        // Scroll to the address section
        section.scrollIntoView({ behavior: 'smooth' });
    }
}

// Copy to clipboard function
function copyToClipboard() {
    const addressElement = document.getElementById('deposit-address');
    if (addressElement) {
        const address = addressElement.textContent;
        
        // Use modern clipboard API if available
        if (navigator.clipboard) {
            navigator.clipboard.writeText(address).then(() => {
                showAlert('Address copied to clipboard!', 'success');
            }).catch(() => {
                fallbackCopyToClipboard(address);
            });
        } else {
            fallbackCopyToClipboard(address);
        }
    }
}

// Fallback copy to clipboard for older browsers
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showAlert('Address copied to clipboard!', 'success');
    } catch (err) {
        console.error('Failed to copy to clipboard:', err);
        showAlert('Failed to copy address. Please copy manually.', 'warning');
    }
    
    document.body.removeChild(textArea);
}

// Load recent deposits
async function loadRecentDeposits() {
    try {
        const response = await apiRequest('/user/deposits/recent');
        
        if (response.success && response.data) {
            displayRecentDeposits(response.data);
        }
    } catch (error) {
        console.error('Error loading recent deposits:', error);
        
        const container = document.getElementById('recent-deposits');
        if (container) {
            container.innerHTML = '<p class="text-muted text-center">No recent deposits found.</p>';
        }
    }
}

// Display recent deposits
function displayRecentDeposits(deposits) {
    const container = document.getElementById('recent-deposits');
    if (!container) return;
    
    if (!deposits || deposits.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No recent deposits found.</p>';
        return;
    }
    
    const html = deposits.map(deposit => `
        <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
            <div>
                <small class="text-muted">${formatDate(deposit.created_at)}</small>
                <div class="fw-bold">${formatCurrency(deposit.amount)}</div>
            </div>
            <span class="badge bg-${getStatusColor(deposit.status)}">${deposit.status}</span>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Get status color for badges
function getStatusColor(status) {
    switch (status.toLowerCase()) {
        case 'completed':
        case 'confirmed':
            return 'success';
        case 'pending':
            return 'warning';
        case 'failed':
        case 'cancelled':
            return 'danger';
        default:
            return 'secondary';
    }
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount || 0);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Show alert message
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at the top of the main content
    const mainContent = document.querySelector('.col-lg-9');
    if (mainContent) {
        mainContent.insertBefore(alertDiv, mainContent.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}
