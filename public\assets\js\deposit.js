// Deposit JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    // Load user data for sidebar
    loadUserData();

    // Load recent deposits
    loadRecentDeposits();

    // Setup form handler
    setupDepositForm();
});

// Cleanup timer when page is unloaded
window.addEventListener('beforeunload', function() {
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }
});

// Load user data for sidebar
async function loadUserData() {
    try {
        const response = await apiRequest('/user/dashboard');
        const data = response.data;

        // Update user info in sidebar
        updateUserInfo(data.user);
        updateUserBalance(data.user.balance);
    } catch (error) {
        console.error('Error loading user data:', error);

        // Check if unauthorized
        if (error.message === 'Unauthorized access. Please login to continue.') {
            logout();
        }
    }
}

// Update user info in sidebar
function updateUserInfo(user) {
    const userNameElement = document.getElementById('user-name');
    const userEmailElement = document.getElementById('user-email');

    if (userNameElement) {
        userNameElement.textContent = user.username || 'User';
    }

    if (userEmailElement) {
        userEmailElement.textContent = user.email || '';
    }
}

// Update user balance in sidebar
function updateUserBalance(balance) {
    const balanceElement = document.getElementById('user-balance');
    if (balanceElement) {
        balanceElement.textContent = formatCurrency(balance || 0);
    }
}

// Setup deposit form
function setupDepositForm() {
    const form = document.getElementById('deposit-form');
    if (!form) return;

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        const amount = formData.get('amount');
        const cryptocurrency = formData.get('cryptocurrency');

        // Validate form
        if (!amount || !cryptocurrency) {
            showAlert('Please fill in all required fields.', 'warning');
            return;
        }

        if (parseFloat(amount) < 10) {
            showAlert('Minimum deposit amount is $10.00', 'warning');
            return;
        }

        // Get submit button reference
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        try {
            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Generating...';
            submitBtn.disabled = true;

            // Generate deposit address (using existing deposit endpoint)
            const response = await apiRequest('/transactions/deposit', 'POST', {
                amount: parseFloat(amount),
                cryptocurrency: cryptocurrency
            });

            console.log('Deposit API Response:', response);

            if (response && response.status === 'success') {
                // Replace form with success card
                replaceFormWithSuccessCard(
                    response.data.deposit_address,
                    cryptocurrency,
                    amount,
                    response.data.expires_in_seconds
                );

                // Show deposit address with countdown timer
                showDepositAddress(
                    response.data.deposit_address,
                    cryptocurrency,
                    amount,
                    response.data.expires_in_seconds
                );
                showAlert('Deposit address generated successfully!', 'success');
            } else {
                console.error('API Response Error:', response);
                throw new Error(response?.message || 'Failed to generate deposit address');
            }
        } catch (error) {
            console.error('Error generating deposit address:', error);
            showAlert(error.message || 'Failed to generate deposit address. Please try again.', 'error');
        } finally {
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });
}

// Replace form with success card
function replaceFormWithSuccessCard(address, cryptocurrency, amount, expiresInSeconds) {
    // Find the card containing the deposit form
    const depositForm = document.getElementById('deposit-form');
    if (!depositForm) return;

    const formCard = depositForm.closest('.card');
    if (!formCard) return;

    // Calculate expiry time
    const expiryTime = new Date(Date.now() + (expiresInSeconds * 1000));
    const formattedExpiryTime = expiryTime.toLocaleString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });

    // Create success card HTML
    const successCardHTML = `
        <div class="card-header bg-success text-white py-3">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle me-2 fs-5"></i>
                <h5 class="mb-0">Deposit Address Generated Successfully!</h5>
            </div>
        </div>
        <div class="card-body">
            <!-- Deposit Summary -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="bg-light p-3 rounded">
                        <h6 class="text-muted mb-2">Deposit Amount</h6>
                        <h4 class="text-primary mb-0">${formatCurrency(amount)}</h4>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="bg-light p-3 rounded">
                        <h6 class="text-muted mb-2">Cryptocurrency</h6>
                        <h4 class="mb-0">
                            <span class="badge bg-primary fs-6">${cryptocurrency}</span>
                        </h4>
                    </div>
                </div>
            </div>

            <!-- Countdown Timer -->
            <div class="mb-4 p-3 bg-warning bg-opacity-10 border border-warning rounded">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <i class="fas fa-clock text-warning me-2"></i>
                        <strong>Time remaining to send deposit:</strong>
                    </div>
                    <div class="countdown-timer">
                        <span id="countdown-display" class="badge bg-warning text-dark fs-6">30:00</span>
                    </div>
                </div>
                <small class="text-muted">
                    Expires at ${formattedExpiryTime}. After expiry, you'll need to generate a new address.
                </small>
            </div>

            <!-- Deposit Address -->
            <div class="mb-4">
                <label class="form-label fw-bold">Send ${cryptocurrency} to this address:</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="deposit-address-input" value="${address}" readonly>
                    <button class="btn btn-outline-primary" type="button" onclick="copyToClipboard()">
                        <i class="fas fa-copy me-1"></i> Copy
                    </button>
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="text-center mb-4">
                <div class="bg-light border rounded p-4 d-inline-block">
                    <i class="fas fa-qrcode fa-4x text-muted mb-2"></i>
                    <div class="small text-muted">QR Code for ${cryptocurrency} Address</div>
                    <div class="small text-muted mt-1">Scan with your wallet app</div>
                </div>
            </div>

            <!-- Important Instructions -->
            <div class="alert alert-info">
                <h6 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>Important Instructions
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="mb-0 small">
                            <li>Only send <strong>${cryptocurrency}</strong> to this address</li>
                            <li>Minimum deposit: <strong>$10.00</strong></li>
                            <li>Network confirmations: <strong>1-3 blocks</strong></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="mb-0 small">
                            <li>Processing time: <strong>5-30 minutes</strong></li>
                            <li>Your deposit will appear as "Pending"</li>
                            <li>Funds credited after confirmation</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex gap-2 flex-wrap">
                <button type="button" class="btn btn-primary" onclick="checkDepositStatus()">
                    <i class="fas fa-sync-alt me-2"></i>Check Status
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="generateNewAddress()">
                    <i class="fas fa-redo me-2"></i>Generate New Address
                </button>
                <a href="${BASE_URL}/transactions" class="btn btn-outline-info">
                    <i class="fas fa-history me-2"></i>View Transactions
                </a>
            </div>
        </div>
    `;

    // Replace the card content
    formCard.innerHTML = successCardHTML;
}

// Show deposit address with countdown timer
function showDepositAddress(address, cryptocurrency, amount, expiresInSeconds = 1800) {
    const section = document.getElementById('deposit-address-section');
    const addressElement = document.getElementById('deposit-address');
    const cryptoElements = document.querySelectorAll('#selected-crypto, #selected-crypto-2');

    if (section && addressElement) {
        addressElement.textContent = address;

        cryptoElements.forEach(element => {
            element.textContent = cryptocurrency;
        });

        // Start countdown timer
        startCountdownTimer(expiresInSeconds);

        section.style.display = 'block';

        // Scroll to the address section
        section.scrollIntoView({ behavior: 'smooth' });
    }
}

// Countdown timer variables
let countdownInterval = null;

// Start countdown timer
function startCountdownTimer(seconds) {
    // Clear any existing timer
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }

    let timeLeft = seconds;
    const countdownDisplay = document.getElementById('countdown-display');
    const timerContainer = countdownDisplay?.closest('.bg-warning');

    if (!countdownDisplay) return;

    // Update timer immediately
    updateCountdownDisplay(timeLeft, countdownDisplay);

    // Start the countdown
    countdownInterval = setInterval(() => {
        timeLeft--;

        if (timeLeft <= 0) {
            // Timer expired
            clearInterval(countdownInterval);
            handleTimerExpiry(timerContainer);
        } else {
            updateCountdownDisplay(timeLeft, countdownDisplay);

            // Change color when less than 5 minutes remaining
            if (timeLeft <= 300) { // 5 minutes
                countdownDisplay.className = 'badge bg-danger text-white fs-6';
                if (timerContainer) {
                    timerContainer.className = timerContainer.className.replace('bg-warning', 'bg-danger');
                    timerContainer.className = timerContainer.className.replace('border-warning', 'border-danger');
                }
            }
        }
    }, 1000);
}

// Update countdown display
function updateCountdownDisplay(seconds, displayElement) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    const timeString = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    displayElement.textContent = timeString;
}

// Handle timer expiry
function handleTimerExpiry(timerContainer) {
    const countdownDisplay = document.getElementById('countdown-display');

    if (countdownDisplay) {
        countdownDisplay.textContent = 'EXPIRED';
        countdownDisplay.className = 'badge bg-danger text-white fs-6';
    }

    if (timerContainer) {
        timerContainer.className = 'mb-3 p-3 bg-danger bg-opacity-10 border border-danger rounded';
        timerContainer.innerHTML = `
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    <strong>Deposit time has expired!</strong>
                </div>
                <div>
                    <span class="badge bg-danger text-white fs-6">EXPIRED</span>
                </div>
            </div>
            <small class="text-muted">
                Please generate a new deposit address to continue with your deposit.
            </small>
        `;
    }

    // Show alert
    showAlert('Deposit time has expired! Please generate a new address to continue.', 'warning');
}

// Generate mock address for demonstration (fallback)
function generateMockAddress(cryptocurrency) {
    const addresses = {
        'USDT': 'TQn9Y2khEsLJW1ChVWFMSMeRDow5CNYY7r',
        'BTC': '**********************************',
        'ETH': '******************************************'
    };

    return addresses[cryptocurrency] || addresses['USDT'];
}

// Copy to clipboard function
function copyToClipboard() {
    // Try to get address from input field first (success card), then from code element (original section)
    const addressInput = document.getElementById('deposit-address-input');
    const addressElement = document.getElementById('deposit-address');

    let address = '';
    if (addressInput) {
        address = addressInput.value;
    } else if (addressElement) {
        address = addressElement.textContent;
    }

    if (address) {
        // Use modern clipboard API if available
        if (navigator.clipboard) {
            navigator.clipboard.writeText(address).then(() => {
                showAlert('Address copied to clipboard!', 'success');
            }).catch(() => {
                fallbackCopyToClipboard(address);
            });
        } else {
            fallbackCopyToClipboard(address);
        }
    }
}

// Fallback copy to clipboard for older browsers
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showAlert('Address copied to clipboard!', 'success');
    } catch (err) {
        console.error('Failed to copy to clipboard:', err);
        showAlert('Failed to copy address. Please copy manually.', 'warning');
    }

    document.body.removeChild(textArea);
}

// Load recent deposits
async function loadRecentDeposits() {
    try {
        // Use existing transactions endpoint with deposit filter
        const response = await apiRequest('/transactions?type=deposit&limit=5');

        if (response.status === 'success' && response.data) {
            displayRecentDeposits(response.data);
        }
    } catch (error) {
        console.error('Error loading recent deposits:', error);

        const container = document.getElementById('recent-deposits');
        if (container) {
            container.innerHTML = '<p class="text-muted text-center">No recent deposits found.</p>';
        }
    }
}

// Display recent deposits
function displayRecentDeposits(deposits) {
    const container = document.getElementById('recent-deposits');
    if (!container) return;

    if (!deposits || deposits.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No recent deposits found.</p>';
        return;
    }

    const html = deposits.map(deposit => `
        <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
            <div>
                <small class="text-muted">${formatDate(deposit.created_at)}</small>
                <div class="fw-bold">${formatCurrency(deposit.amount)}</div>
            </div>
            <span class="badge bg-${getStatusColor(deposit.status)}">${deposit.status}</span>
        </div>
    `).join('');

    container.innerHTML = html;
}

// Get status color for badges
function getStatusColor(status) {
    switch (status.toLowerCase()) {
        case 'completed':
        case 'confirmed':
            return 'success';
        case 'pending':
            return 'warning';
        case 'failed':
        case 'cancelled':
            return 'danger';
        default:
            return 'secondary';
    }
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount || 0);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Check deposit status
function checkDepositStatus() {
    showAlert('Checking deposit status...', 'info');

    // Reload recent deposits to show any updates
    loadRecentDeposits();

    // In a real application, this would check the specific transaction status
    setTimeout(() => {
        showAlert('Deposit status updated. Check your transaction history for the latest information.', 'success');
    }, 1000);
}

// Generate new address
function generateNewAddress() {
    if (confirm('Are you sure you want to generate a new deposit address? The current address will expire immediately.')) {
        // Reload the page to show the form again
        window.location.reload();
    }
}

// Show alert message
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at the top of the main content
    const mainContent = document.querySelector('.col-lg-9');
    if (mainContent) {
        mainContent.insertBefore(alertDiv, mainContent.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}
