// Home JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    // Update crypto prices
    updateCryptoPrices();
    
    // Update prices every 60 seconds
    setInterval(updateCryptoPrices, 60000);
});

// Update cryptocurrency prices
async function updateCryptoPrices() {
    try {
        // In a real application, this would fetch from a cryptocurrency API
        // For demo purposes, we'll use random price fluctuations
        
        // Bitcoin price (random fluctuation between $29,000 and $30,000)
        const btcPrice = 29000 + Math.random() * 1000;
        const btcChange = (Math.random() * 2 - 1).toFixed(2); // Random change between -1% and 1%
        
        // Ethereum price (random fluctuation between $1,800 and $1,900)
        const ethPrice = 1800 + Math.random() * 100;
        const ethChange = (Math.random() * 2 - 1).toFixed(2); // Random change between -1% and 1%
        
        // Tether price (random fluctuation between $0.99 and $1.01)
        const usdtPrice = 0.99 + Math.random() * 0.02;
        const usdtChange = (Math.random() * 0.1 - 0.05).toFixed(2); // Random change between -0.05% and 0.05%
        
        // Update price elements
        updatePriceElement('btc-price', btcPrice, btcChange);
        updatePriceElement('eth-price', ethPrice, ethChange);
        updatePriceElement('usdt-price', usdtPrice, usdtChange);
    } catch (error) {
        console.error('Error updating crypto prices:', error);
    }
}

// Update price element
function updatePriceElement(elementId, price, change) {
    const priceElement = document.getElementById(elementId);
    const changeElement = priceElement?.nextElementSibling;
    
    if (priceElement) {
        priceElement.textContent = formatCurrency(price);
    }
    
    if (changeElement) {
        changeElement.textContent = `${change > 0 ? '+' : ''}${change}%`;
        
        // Update color based on change
        if (parseFloat(change) > 0) {
            changeElement.classList.remove('text-danger');
            changeElement.classList.add('text-success');
        } else if (parseFloat(change) < 0) {
            changeElement.classList.remove('text-success');
            changeElement.classList.add('text-danger');
        }
    }
}
