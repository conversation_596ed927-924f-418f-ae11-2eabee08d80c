// Investments JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    // Load user data for sidebar
    loadUserData();

    // Load investments data
    loadInvestments();
});

// Load user data for sidebar
async function loadUserData() {
    try {
        const response = await apiRequest('/user/dashboard');
        const data = response.data;

        // Update user info in sidebar
        updateUserInfo(data.user);
        updateUserBalance(data.user.balance);
    } catch (error) {
        console.error('Error loading user data:', error);

        // Check if unauthorized
        if (error.message === 'Unauthorized access. Please login to continue.') {
            logout();
        }
    }
}

// Update user info in sidebar
function updateUserInfo(user) {
    const userNameElement = document.getElementById('user-name');
    const userEmailElement = document.getElementById('user-email');

    if (userNameElement) {
        userNameElement.textContent = user.username || 'User';
    }

    if (userEmailElement) {
        userEmailElement.textContent = user.email || '';
    }
}

// Update user balance in sidebar
function updateUserBalance(balance) {
    const balanceElement = document.getElementById('user-balance');
    if (balanceElement) {
        balanceElement.textContent = formatCurrency(balance || 0);
    }
}

// Load investments
async function loadInvestments() {
    try {
        const response = await apiRequest('/investments');

        if (response.success && response.data) {
            displayInvestments(response.data);
        }
    } catch (error) {
        console.error('Error loading investments:', error);

        const tableBody = document.getElementById('investments-table');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-muted">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load investments. Please refresh the page.
                    </td>
                </tr>
            `;
        }

        // Check if unauthorized
        if (error.message === 'Unauthorized access. Please login to continue.') {
            logout();
        }
    }
}

// Display investments
function displayInvestments(investments) {
    const tableBody = document.getElementById('investments-table');
    if (!tableBody) return;

    if (!investments || investments.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4 text-muted">
                    <i class="fas fa-chart-line me-2"></i>
                    No investments found. <a href="${BASE_URL}/plans" class="text-decoration-none">Start investing now</a>
                </td>
            </tr>
        `;
        return;
    }

    const html = investments.map(investment => `
        <tr>
            <td>
                <div class="fw-bold">${investment.plan_name}</div>
                <small class="text-muted">${investment.plan_description || ''}</small>
            </td>
            <td>
                <span class="fw-bold">${formatCurrency(investment.amount)}</span>
            </td>
            <td>
                <span class="text-success fw-bold">${investment.roi_percentage}%</span>
                <div class="small text-muted">${formatCurrency(investment.expected_return)}</div>
            </td>
            <td>
                <div>${formatDate(investment.start_date)}</div>
                <small class="text-muted">${getTimeAgo(investment.start_date)}</small>
            </td>
            <td>
                <div>${formatDate(investment.end_date)}</div>
                <small class="text-muted">${getTimeRemaining(investment.end_date)}</small>
            </td>
            <td>
                <span class="badge bg-${getStatusColor(investment.status)}">${investment.status}</span>
                <div class="small text-muted mt-1">
                    ${getProgressBar(investment.start_date, investment.end_date)}
                </div>
            </td>
            <td>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="viewInvestmentDetails(${investment.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${investment.status === 'active' ? `
                        <button type="button" class="btn btn-outline-success" onclick="withdrawEarnings(${investment.id})">
                            <i class="fas fa-money-bill-wave"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = html;
}

// Get status color for badges
function getStatusColor(status) {
    switch (status.toLowerCase()) {
        case 'active':
            return 'success';
        case 'completed':
            return 'primary';
        case 'pending':
            return 'warning';
        case 'cancelled':
            return 'danger';
        default:
            return 'secondary';
    }
}

// Get progress bar for investment duration
function getProgressBar(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const now = new Date();

    const totalDuration = end - start;
    const elapsed = now - start;
    const progress = Math.min(Math.max((elapsed / totalDuration) * 100, 0), 100);

    return `
        <div class="progress" style="height: 4px;">
            <div class="progress-bar bg-primary" role="progressbar" style="width: ${progress}%"></div>
        </div>
    `;
}

// Get time ago
function getTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 30) return `${diffDays} days ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
}

// Get time remaining
function getTimeRemaining(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date - now;

    if (diffTime <= 0) return 'Completed';

    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day left';
    if (diffDays < 30) return `${diffDays} days left`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months left`;
    return `${Math.floor(diffDays / 365)} years left`;
}

// View investment details
function viewInvestmentDetails(investmentId) {
    // TODO: Implement investment details modal or page
    console.log('View investment details:', investmentId);
    showAlert('Investment details feature coming soon!', 'info');
}

// Withdraw earnings
async function withdrawEarnings(investmentId) {
    if (!confirm('Are you sure you want to withdraw earnings from this investment?')) {
        return;
    }

    try {
        const response = await apiRequest(`/user/investments/${investmentId}/withdraw`, 'POST');

        if (response.success) {
            showAlert('Withdrawal request submitted successfully!', 'success');
            loadInvestments(); // Reload investments
        } else {
            throw new Error(response.message || 'Failed to process withdrawal');
        }
    } catch (error) {
        console.error('Error withdrawing earnings:', error);
        showAlert(error.message || 'Failed to process withdrawal. Please try again.', 'error');
    }
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount || 0);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });
}

// Show alert message
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at the top of the main content
    const mainContent = document.querySelector('.col-lg-9');
    if (mainContent) {
        mainContent.insertBefore(alertDiv, mainContent.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}
