// Login JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    // Remove client-side redirect check - let server handle authentication
    // Server-side authentication is more reliable and prevents redirect loops

    const loginForm = document.getElementById('login-form');
    const loginButton = document.getElementById('login-button');
    const loginSpinner = document.getElementById('login-spinner');
    const loginError = document.getElementById('login-error');

    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Clear previous errors
            loginError.classList.add('d-none');
            loginError.textContent = '';

            // Show loading state
            loginButton.disabled = true;
            loginSpinner.classList.remove('d-none');

            // Get form data
            const formData = {
                username: document.getElementById('username').value,
                password: document.getElementById('password').value
            };

            try {
                // Make login request
                const response = await apiRequest('/auth/login', 'POST', formData);

                // Store token and user data
                localStorage.setItem('token', response.data.token);
                localStorage.setItem('user', JSON.stringify(response.data.user));

                // Redirect to dashboard
                window.location.href = '/dashboard';
            } catch (error) {
                // Show error message
                loginError.textContent = error.message || 'Login failed. Please check your credentials.';
                loginError.classList.remove('d-none');

                // Reset loading state
                loginButton.disabled = false;
                loginSpinner.classList.add('d-none');
            }
        });
    }
});
