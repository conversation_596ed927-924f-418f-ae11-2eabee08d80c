// Main JavaScript file

// API URL
const API_URL = '/api';

// Helper function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

// Helper function to format date
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString('en-US', options);
}

// Helper function to get status badge
function getStatusBadge(status) {
    let badgeClass = '';

    switch (status) {
        case 'active':
        case 'completed':
        case 'paid':
            badgeClass = 'bg-success';
            break;
        case 'pending':
            badgeClass = 'bg-warning';
            break;
        case 'cancelled':
        case 'failed':
            badgeClass = 'bg-danger';
            break;
        default:
            badgeClass = 'bg-secondary';
    }

    return `<span class="badge ${badgeClass}">${status.charAt(0).toUpperCase() + status.slice(1)}</span>`;
}

// Helper function to get transaction type badge
function getTransactionTypeBadge(type) {
    let badgeClass = '';
    let icon = '';

    switch (type) {
        case 'deposit':
            badgeClass = 'bg-success';
            icon = 'fa-arrow-down';
            break;
        case 'withdrawal':
            badgeClass = 'bg-danger';
            icon = 'fa-arrow-up';
            break;
        case 'investment':
            badgeClass = 'bg-primary';
            icon = 'fa-chart-line';
            break;
        case 'earning':
            badgeClass = 'bg-warning';
            icon = 'fa-coins';
            break;
        case 'referral':
            badgeClass = 'bg-info';
            icon = 'fa-user-plus';
            break;
        default:
            badgeClass = 'bg-secondary';
            icon = 'fa-exchange-alt';
    }

    return `<span class="badge ${badgeClass}"><i class="fas ${icon} me-1"></i>${type.charAt(0).toUpperCase() + type.slice(1)}</span>`;
}

// Helper function to make API requests
async function apiRequest(endpoint, method = 'GET', data = null) {
    const token = localStorage.getItem('token');

    const headers = {
        'Content-Type': 'application/json'
    };

    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    const options = {
        method,
        headers
    };

    if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data);
    }

    try {
        const response = await fetch(`${API_URL}${endpoint}`, options);

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            const text = await response.text();
            console.error('Non-JSON response:', text);
            throw new Error('Server returned non-JSON response. Check server logs for details.');
        }

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || 'An error occurred');
        }

        return result;
    } catch (error) {
        console.error('API Request Error:', error);
        throw error;
    }
}

// Toggle password visibility
document.addEventListener('click', function(e) {
    if (e.target.closest('.toggle-password')) {
        const button = e.target.closest('.toggle-password');
        const input = button.parentElement.querySelector('input');
        const icon = button.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }
});

// Check if user is logged in
function isLoggedIn() {
    return localStorage.getItem('token') !== null;
}

// Logout function
async function logout() {
    // Clear client-side storage FIRST to prevent redirect loops
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.clear(); // Clear all localStorage to be safe

    try {
        // Call logout API to clear server session
        await apiRequest('/auth/logout', 'POST');
    } catch (error) {
        console.error('Logout API error:', error);
        // Continue with logout even if API fails
    }

    // Redirect to login page
    window.location.href = '/login';
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add logout event listener
    const logoutLink = document.querySelector('a[href="/logout"]');
    if (logoutLink) {
        logoutLink.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
});
