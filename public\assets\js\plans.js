// Plans JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    // Load investment plans
    loadInvestmentPlans();
});

// Load investment plans
async function loadInvestmentPlans() {
    const plansContainer = document.getElementById('plans-container');
    
    try {
        // Get investment plans
        const response = await apiRequest('/investments/plans');
        const plans = response.data;
        
        // Clear loading indicator
        plansContainer.innerHTML = '';
        
        if (plans.length === 0) {
            plansContainer.innerHTML = `
                <div class="col-12 text-center">
                    <p class="lead">No investment plans available at the moment. Please check back later.</p>
                </div>
            `;
            return;
        }
        
        // Display plans
        plans.forEach(plan => {
            const planCard = createPlanCard(plan);
            plansContainer.appendChild(planCard);
        });
    } catch (error) {
        console.error('Error loading investment plans:', error);
        
        plansContainer.innerHTML = `
            <div class="col-12 text-center">
                <p class="lead text-danger">Error loading investment plans. Please try again later.</p>
            </div>
        `;
    }
}

// Create plan card
function createPlanCard(plan) {
    const col = document.createElement('div');
    col.className = 'col-md-4 mb-4';
    
    const isPopular = plan.name.toLowerCase().includes('starter');
    
    col.innerHTML = `
        <div class="card h-100 border-0 shadow position-relative">
            ${isPopular ? '<div class="position-absolute top-0 start-50 translate-middle badge bg-primary px-3 py-2">Popular</div>' : ''}
            <div class="card-body p-4 text-center">
                <h3 class="card-title mb-3">${plan.name}</h3>
                <p class="text-muted mb-4">${plan.description || 'Invest in our carefully curated plan for optimal returns.'}</p>
                
                <div class="pricing mb-4">
                    <h2 class="display-4 fw-bold text-primary">${plan.roi_percentage}%</h2>
                    <p class="text-muted">APY</p>
                </div>
                
                <div class="features mb-4">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Min. Investment:</span>
                        <span class="fw-bold">$${plan.min_amount}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Max. Investment:</span>
                        <span class="fw-bold">${plan.max_amount ? '$' + plan.max_amount : 'Unlimited'}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Duration:</span>
                        <span class="fw-bold">${plan.duration_days} days</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Daily ROI:</span>
                        <span class="fw-bold">${plan.daily_roi}%</span>
                    </div>
                </div>
                
                <div class="d-grid">
                    <a href="${BASE_URL}/register" class="btn btn-primary">Start Investing</a>
                </div>
            </div>
        </div>
    `;
    
    return col;
}
