// Profile JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    // Load user data for sidebar and profile
    loadUserData();

    // Setup forms
    setupProfileForm();
    setupPasswordForm();
});

// Load user data for sidebar and profile
async function loadUserData() {
    try {
        const response = await apiRequest('/user/profile');
        const data = response.data;

        // Update user info in sidebar
        updateUserInfo(data);
        updateUserBalance(data.balance);

        // Update profile form
        updateProfileForm(data);

        // Update account information
        updateAccountInfo(data);
    } catch (error) {
        console.error('Error loading user data:', error);

        // Check if unauthorized
        if (error.message === 'Unauthorized access. Please login to continue.') {
            logout();
        }
    }
}

// Update user info in sidebar
function updateUserInfo(user) {
    const userNameElement = document.getElementById('user-name');
    const userEmailElement = document.getElementById('user-email');

    if (userNameElement) {
        userNameElement.textContent = user.username || 'User';
    }

    if (userEmailElement) {
        userEmailElement.textContent = user.email || '';
    }
}

// Update user balance in sidebar
function updateUserBalance(balance) {
    const balanceElement = document.getElementById('user-balance');
    if (balanceElement) {
        balanceElement.textContent = formatCurrency(balance || 0);
    }
}

// Update profile form
function updateProfileForm(user) {
    const form = document.getElementById('profile-form');
    if (!form) return;

    // Fill form fields
    const fields = ['first_name', 'last_name', 'username', 'email'];
    fields.forEach(field => {
        const input = form.querySelector(`#${field}`);
        if (input && user[field]) {
            input.value = user[field];
        }
    });
}

// Update account information
function updateAccountInfo(user) {
    // Update account balance
    const balanceElement = document.getElementById('account-balance');
    if (balanceElement) {
        balanceElement.textContent = formatCurrency(user.balance || 0);
    }

    // Update account status
    const statusElement = document.getElementById('account-status');
    if (statusElement) {
        statusElement.textContent = user.status || 'Active';
        statusElement.className = `badge bg-${user.status === 'active' ? 'success' : 'warning'}`;
    }

    // Update member since
    const memberSinceElement = document.getElementById('member-since');
    if (memberSinceElement && user.created_at) {
        memberSinceElement.textContent = formatDate(user.created_at);
    }

    // Update last login
    const lastLoginElement = document.getElementById('last-login');
    if (lastLoginElement && user.last_login) {
        lastLoginElement.textContent = formatDateTime(user.last_login);
    }
}

// Setup profile form
function setupProfileForm() {
    const form = document.getElementById('profile-form');
    if (!form) return;

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        const data = {
            first_name: formData.get('first_name'),
            last_name: formData.get('last_name')
        };

        // Validate form
        if (!data.first_name || !data.last_name) {
            showAlert('Please fill in all required fields.', 'warning');
            return;
        }

        try {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Updating...';
            submitBtn.disabled = true;

            // Update profile
            const response = await apiRequest('/user/profile', 'PUT', data);

            if (response.success) {
                showAlert('Profile updated successfully!', 'success');

                // Reload user data
                loadUserData();
            } else {
                throw new Error(response.message || 'Failed to update profile');
            }
        } catch (error) {
            console.error('Error updating profile:', error);
            showAlert(error.message || 'Failed to update profile. Please try again.', 'error');
        } finally {
            // Reset button
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = 'Update Profile';
            submitBtn.disabled = false;
        }
    });
}

// Setup password form
function setupPasswordForm() {
    const form = document.getElementById('password-form');
    if (!form) return;

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        const data = {
            current_password: formData.get('current_password'),
            new_password: formData.get('new_password'),
            confirm_password: formData.get('confirm_password')
        };

        // Validate form
        if (!data.current_password || !data.new_password || !data.confirm_password) {
            showAlert('Please fill in all password fields.', 'warning');
            return;
        }

        if (data.new_password !== data.confirm_password) {
            showAlert('New password and confirmation do not match.', 'warning');
            return;
        }

        if (data.new_password.length < 6) {
            showAlert('New password must be at least 6 characters long.', 'warning');
            return;
        }

        try {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Changing...';
            submitBtn.disabled = true;

            // Change password
            const response = await apiRequest('/user/change-password', 'POST', {
                current_password: data.current_password,
                new_password: data.new_password
            });

            if (response.success) {
                showAlert('Password changed successfully!', 'success');

                // Reset form
                form.reset();
            } else {
                throw new Error(response.message || 'Failed to change password');
            }
        } catch (error) {
            console.error('Error changing password:', error);
            showAlert(error.message || 'Failed to change password. Please try again.', 'error');
        } finally {
            // Reset button
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = 'Change Password';
            submitBtn.disabled = false;
        }
    });
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount || 0);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
    });
}

// Format date and time
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Show alert message
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at the top of the main content
    const mainContent = document.querySelector('.col-lg-9');
    if (mainContent) {
        mainContent.insertBefore(alertDiv, mainContent.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}
