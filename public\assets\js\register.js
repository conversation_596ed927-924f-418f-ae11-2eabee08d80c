// Register JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    // Check if already logged in
    if (isLoggedIn()) {
        window.location.href = '/dashboard';
        return;
    }
    
    const registerForm = document.getElementById('register-form');
    const registerButton = document.getElementById('register-button');
    const registerSpinner = document.getElementById('register-spinner');
    const registerError = document.getElementById('register-error');
    
    if (registerForm) {
        registerForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Clear previous errors
            registerError.classList.add('d-none');
            registerError.textContent = '';
            
            // Reset validation errors
            document.getElementById('username-error').textContent = '';
            document.getElementById('email-error').textContent = '';
            document.getElementById('password-error').textContent = '';
            document.getElementById('password-confirmation-error').textContent = '';
            document.getElementById('terms-error').textContent = '';
            
            // Show loading state
            registerButton.disabled = true;
            registerSpinner.classList.remove('d-none');
            
            // Get form data
            const formData = {
                username: document.getElementById('username').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                password_confirmation: document.getElementById('password_confirmation').value
            };
            
            // Validate terms
            if (!document.getElementById('terms').checked) {
                document.getElementById('terms-error').textContent = 'You must agree to the terms and conditions';
                registerButton.disabled = false;
                registerSpinner.classList.add('d-none');
                return;
            }
            
            try {
                // Make register request
                const response = await apiRequest('/auth/register', 'POST', formData);
                
                // Store token and user data
                localStorage.setItem('token', response.data.token);
                localStorage.setItem('user', JSON.stringify(response.data.user));
                
                // Redirect to dashboard
                window.location.href = '/dashboard';
            } catch (error) {
                // Handle validation errors
                if (error.errors) {
                    for (const field in error.errors) {
                        const errorElement = document.getElementById(`${field}-error`);
                        if (errorElement) {
                            errorElement.textContent = error.errors[field][0];
                        }
                    }
                } else {
                    // Show general error message
                    registerError.textContent = error.message || 'Registration failed. Please try again.';
                    registerError.classList.remove('d-none');
                }
                
                // Reset loading state
                registerButton.disabled = false;
                registerSpinner.classList.add('d-none');
            }
        });
    }
});
