// Support JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    // Load user data for sidebar
    loadUserData();
    
    // Load support tickets
    loadSupportTickets();
    
    // Setup support form
    setupSupportForm();
});

// Load user data for sidebar
async function loadUserData() {
    try {
        const response = await apiRequest('/user/dashboard');
        const data = response.data;
        
        // Update user info in sidebar
        updateUserInfo(data.user);
        updateUserBalance(data.user.balance);
    } catch (error) {
        console.error('Error loading user data:', error);
        
        // Check if unauthorized
        if (error.message === 'Unauthorized access. Please login to continue.') {
            logout();
        }
    }
}

// Update user info in sidebar
function updateUserInfo(user) {
    const userNameElement = document.getElementById('user-name');
    const userEmailElement = document.getElementById('user-email');
    
    if (userNameElement) {
        userNameElement.textContent = user.username || 'User';
    }
    
    if (userEmailElement) {
        userEmailElement.textContent = user.email || '';
    }
}

// Update user balance in sidebar
function updateUserBalance(balance) {
    const balanceElement = document.getElementById('user-balance');
    if (balanceElement) {
        balanceElement.textContent = formatCurrency(balance || 0);
    }
}

// Setup support form
function setupSupportForm() {
    const form = document.getElementById('support-form');
    if (!form) return;
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const subject = formData.get('subject');
        const priority = formData.get('priority');
        const message = formData.get('message');
        
        // Validate form
        if (!subject || !priority || !message) {
            showAlert('Please fill in all required fields.', 'warning');
            return;
        }
        
        if (message.length < 10) {
            showAlert('Please provide a more detailed message (at least 10 characters).', 'warning');
            return;
        }
        
        try {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Sending...';
            submitBtn.disabled = true;
            
            // Submit support ticket
            const response = await apiRequest('/user/support/create-ticket', {
                method: 'POST',
                body: JSON.stringify({
                    subject: subject,
                    priority: priority,
                    message: message
                })
            });
            
            if (response.success) {
                showAlert('Support ticket created successfully! We will respond within 24 hours.', 'success');
                form.reset();
                
                // Reload support tickets
                loadSupportTickets();
            } else {
                throw new Error(response.message || 'Failed to create support ticket');
            }
        } catch (error) {
            console.error('Error creating support ticket:', error);
            showAlert(error.message || 'Failed to create support ticket. Please try again.', 'error');
        } finally {
            // Reset button
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });
}

// Load support tickets
async function loadSupportTickets() {
    try {
        const response = await apiRequest('/user/support/tickets');
        
        if (response.success && response.data) {
            displaySupportTickets(response.data);
        }
    } catch (error) {
        console.error('Error loading support tickets:', error);
        
        const container = document.getElementById('support-tickets');
        if (container) {
            container.innerHTML = '<p class="text-muted text-center">No support tickets found.</p>';
        }
    }
}

// Display support tickets
function displaySupportTickets(tickets) {
    const container = document.getElementById('support-tickets');
    if (!container) return;
    
    if (!tickets || tickets.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No support tickets found.</p>';
        return;
    }
    
    const html = tickets.map(ticket => `
        <div class="card border-0 mb-3 shadow-sm">
            <div class="card-body p-3">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">#${ticket.id} - ${getSubjectText(ticket.subject)}</h6>
                    <span class="badge bg-${getPriorityColor(ticket.priority)}">${ticket.priority}</span>
                </div>
                <p class="text-muted small mb-2">${truncateText(ticket.message, 100)}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">${formatDate(ticket.created_at)}</small>
                    <span class="badge bg-${getStatusColor(ticket.status)}">${ticket.status}</span>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Get subject text
function getSubjectText(subject) {
    const subjects = {
        'account': 'Account Issues',
        'deposit': 'Deposit Problems',
        'withdrawal': 'Withdrawal Issues',
        'investment': 'Investment Questions',
        'technical': 'Technical Support',
        'other': 'Other'
    };
    
    return subjects[subject] || subject;
}

// Get priority color
function getPriorityColor(priority) {
    switch (priority.toLowerCase()) {
        case 'urgent':
            return 'danger';
        case 'high':
            return 'warning';
        case 'medium':
            return 'info';
        case 'low':
            return 'secondary';
        default:
            return 'secondary';
    }
}

// Get status color for badges
function getStatusColor(status) {
    switch (status.toLowerCase()) {
        case 'resolved':
        case 'closed':
            return 'success';
        case 'open':
        case 'in_progress':
            return 'primary';
        case 'pending':
            return 'warning';
        default:
            return 'secondary';
    }
}

// Truncate text
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + '...';
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount || 0);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Show alert message
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at the top of the main content
    const mainContent = document.querySelector('.col-lg-9');
    if (mainContent) {
        mainContent.insertBefore(alertDiv, mainContent.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}
