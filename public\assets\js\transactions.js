// Transactions JavaScript file

let currentFilter = 'all';

document.addEventListener('DOMContentLoaded', function() {
    // Load user data for sidebar
    loadUserData();

    // Load transactions data
    loadTransactions();

    // Setup filter buttons
    setupFilterButtons();
});

// Load user data for sidebar
async function loadUserData() {
    try {
        const response = await apiRequest('/user/dashboard');
        const data = response.data;

        // Update user info in sidebar
        updateUserInfo(data.user);
        updateUserBalance(data.user.balance);
    } catch (error) {
        console.error('Error loading user data:', error);

        // Check if unauthorized
        if (error.message === 'Unauthorized access. Please login to continue.') {
            logout();
        }
    }
}

// Update user info in sidebar
function updateUserInfo(user) {
    const userNameElement = document.getElementById('user-name');
    const userEmailElement = document.getElementById('user-email');

    if (userNameElement) {
        userNameElement.textContent = user.username || 'User';
    }

    if (userEmailElement) {
        userEmailElement.textContent = user.email || '';
    }
}

// Update user balance in sidebar
function updateUserBalance(balance) {
    const balanceElement = document.getElementById('user-balance');
    if (balanceElement) {
        balanceElement.textContent = formatCurrency(balance || 0);
    }
}

// Setup filter buttons
function setupFilterButtons() {
    const filterButtons = document.querySelectorAll('[data-filter]');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Update current filter
            currentFilter = this.getAttribute('data-filter');

            // Reload transactions with filter
            loadTransactions();
        });
    });
}

// Load transactions
async function loadTransactions() {
    try {
        const url = currentFilter === 'all'
            ? '/transactions'
            : `/transactions?type=${currentFilter}`;

        const response = await apiRequest(url);

        if (response.status === 'success' && response.data) {
            displayTransactions(response.data);
        }
    } catch (error) {
        console.error('Error loading transactions:', error);

        const tableBody = document.getElementById('transactions-table');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4 text-muted">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load transactions. Please refresh the page.
                    </td>
                </tr>
            `;
        }

        // Check if unauthorized
        if (error.message === 'Unauthorized access. Please login to continue.') {
            logout();
        }
    }
}

// Display transactions
function displayTransactions(transactions) {
    const tableBody = document.getElementById('transactions-table');
    if (!tableBody) return;

    if (!transactions || transactions.length === 0) {
        const filterText = currentFilter === 'all' ? 'transactions' : `${currentFilter} transactions`;
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-4 text-muted">
                    <i class="fas fa-exchange-alt me-2"></i>
                    No ${filterText} found.
                </td>
            </tr>
        `;
        return;
    }

    const html = transactions.map(transaction => `
        <tr>
            <td>
                <div>${formatDate(transaction.created_at)}</div>
                <small class="text-muted">${formatTime(transaction.created_at)}</small>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="fas fa-${getTypeIcon(transaction.type)} me-2 text-${getTypeColor(transaction.type)}"></i>
                    <div>
                        <div class="fw-bold">${getTypeText(transaction.type)}</div>
                        <small class="text-muted">${transaction.description || ''}</small>
                    </div>
                </div>
            </td>
            <td>
                <span class="fw-bold text-${getAmountColor(transaction.type)}">${formatAmount(transaction.amount, transaction.type)}</span>
                ${transaction.fee > 0 ? `<div class="small text-muted">Fee: ${formatCurrency(transaction.fee)}</div>` : ''}
            </td>
            <td>
                <span class="badge bg-${getStatusColor(transaction.status)}">${transaction.status}</span>
            </td>
            <td>
                <code class="small">${transaction.reference || transaction.id}</code>
            </td>
            <td>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="viewTransactionDetails(${transaction.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${transaction.status === 'pending' && transaction.type === 'withdrawal' ? `
                        <button type="button" class="btn btn-outline-danger" onclick="cancelTransaction(${transaction.id})">
                            <i class="fas fa-times"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = html;
}

// Get type icon
function getTypeIcon(type) {
    switch (type.toLowerCase()) {
        case 'deposit':
            return 'arrow-down';
        case 'withdrawal':
            return 'arrow-up';
        case 'investment':
            return 'chart-line';
        case 'earning':
            return 'coins';
        default:
            return 'exchange-alt';
    }
}

// Get type color
function getTypeColor(type) {
    switch (type.toLowerCase()) {
        case 'deposit':
        case 'earning':
            return 'success';
        case 'withdrawal':
            return 'danger';
        case 'investment':
            return 'primary';
        default:
            return 'secondary';
    }
}

// Get type text
function getTypeText(type) {
    switch (type.toLowerCase()) {
        case 'deposit':
            return 'Deposit';
        case 'withdrawal':
            return 'Withdrawal';
        case 'investment':
            return 'Investment';
        case 'earning':
            return 'Earning';
        default:
            return type;
    }
}

// Get amount color
function getAmountColor(type) {
    switch (type.toLowerCase()) {
        case 'deposit':
        case 'earning':
            return 'success';
        case 'withdrawal':
        case 'investment':
            return 'danger';
        default:
            return 'dark';
    }
}

// Format amount with sign
function formatAmount(amount, type) {
    const sign = ['deposit', 'earning'].includes(type.toLowerCase()) ? '+' : '-';
    return sign + formatCurrency(Math.abs(amount));
}

// Get status color for badges
function getStatusColor(status) {
    switch (status.toLowerCase()) {
        case 'completed':
        case 'confirmed':
            return 'success';
        case 'pending':
            return 'warning';
        case 'failed':
        case 'cancelled':
            return 'danger';
        case 'processing':
            return 'info';
        default:
            return 'secondary';
    }
}

// View transaction details
function viewTransactionDetails(transactionId) {
    // TODO: Implement transaction details modal or page
    console.log('View transaction details:', transactionId);
    showAlert('Transaction details feature coming soon!', 'info');
}

// Cancel transaction
async function cancelTransaction(transactionId) {
    if (!confirm('Are you sure you want to cancel this transaction?')) {
        return;
    }

    try {
        const response = await apiRequest(`/user/transactions/${transactionId}/cancel`, 'POST');

        if (response.status === 'success') {
            showAlert('Transaction cancelled successfully!', 'success');
            loadTransactions(); // Reload transactions
        } else {
            throw new Error(response.message || 'Failed to cancel transaction');
        }
    } catch (error) {
        console.error('Error cancelling transaction:', error);
        showAlert(error.message || 'Failed to cancel transaction. Please try again.', 'error');
    }
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount || 0);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });
}

// Format time
function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Show alert message
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at the top of the main content
    const mainContent = document.querySelector('.col-lg-9');
    if (mainContent) {
        mainContent.insertBefore(alertDiv, mainContent.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}
