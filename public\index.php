<?php

// Load bootstrap file
require_once __DIR__ . '/../app/bootstrap.php';

// Define base URL
define('BASE_URL', App\Config\App::$appUrl);

// Simple router
$request = $_SERVER['REQUEST_URI'];
$request = strtok($request, '?');

// Remove base path from request
$basePath = parse_url(BASE_URL, PHP_URL_PATH) ?? '';
if (!empty($basePath) && strpos($request, $basePath) === 0) {
    $request = substr($request, strlen($basePath));
}

// Routes
switch ($request) {
    case '/':
    case '':
        require __DIR__ . '/views/home.php';
        break;
    case '/login':
        require __DIR__ . '/views/login.php';
        break;
    case '/register':
        require __DIR__ . '/views/register.php';
        break;
    case '/dashboard':
        require __DIR__ . '/views/dashboard.php';
        break;
    case '/investments':
        require __DIR__ . '/views/investments.php';
        break;
    case '/transactions':
        require __DIR__ . '/views/transactions.php';
        break;
    case '/profile':
        require __DIR__ . '/views/profile.php';
        break;
    case '/admin':
        require __DIR__ . '/views/admin/index.php';
        break;
    // New routes
    case '/about':
        require __DIR__ . '/views/about.php';
        break;
    case '/plans':
        require __DIR__ . '/views/plans.php';
        break;
    case '/faq':
        require __DIR__ . '/views/faq.php';
        break;
    case '/contact':
        require __DIR__ . '/views/contact.php';
        break;
    case '/logout':
        // Clear session
        session_unset();
        session_destroy();

        // Redirect to home page
        header('Location: ' . BASE_URL . '/');
        exit;
        break;
    default:
        http_response_code(404);
        require __DIR__ . '/views/404.php';
        break;
}
