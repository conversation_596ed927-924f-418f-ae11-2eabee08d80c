<?php
$title = 'Contact Us - CryptoInvest';

// Extra JS
$extraJs = '<script src="' . BASE_URL . '/assets/js/contact.js"></script>';

// Start output buffering
ob_start();
?>

<section class="contact-header py-5 bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-4 fw-bold">Contact Us</h1>
                <p class="lead">We're here to help with any questions you may have</p>
            </div>
        </div>
    </div>
</section>

<section class="contact-content py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="row">
                    <div class="col-md-5 mb-4 mb-md-0">
                        <h3 class="mb-4">Get in Touch</h3>
                        
                        <div class="d-flex mb-4">
                            <div class="me-3">
                                <div class="bg-primary-light text-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-envelope"></i>
                                </div>
                            </div>
                            <div>
                                <h5>Email</h5>
                                <p class="mb-0"><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="d-flex mb-4">
                            <div class="me-3">
                                <div class="bg-primary-light text-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-phone"></i>
                                </div>
                            </div>
                            <div>
                                <h5>Phone</h5>
                                <p class="mb-0">+****************</p>
                            </div>
                        </div>
                        
                        <div class="d-flex mb-4">
                            <div class="me-3">
                                <div class="bg-primary-light text-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                            </div>
                            <div>
                                <h5>Address</h5>
                                <p class="mb-0">123 Crypto Street<br>Digital City, DC 12345</p>
                            </div>
                        </div>
                        
                        <div class="d-flex mb-4">
                            <div class="me-3">
                                <div class="bg-primary-light text-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                            <div>
                                <h5>Business Hours</h5>
                                <p class="mb-0">Monday - Friday: 9:00 AM - 5:00 PM<br>Saturday - Sunday: Closed</p>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5 class="mb-3">Follow Us</h5>
                            <div class="d-flex">
                                <a href="#" class="me-3 text-primary"><i class="fab fa-facebook-f fa-lg"></i></a>
                                <a href="#" class="me-3 text-primary"><i class="fab fa-twitter fa-lg"></i></a>
                                <a href="#" class="me-3 text-primary"><i class="fab fa-instagram fa-lg"></i></a>
                                <a href="#" class="text-primary"><i class="fab fa-linkedin-in fa-lg"></i></a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-7">
                        <div class="card border-0 shadow">
                            <div class="card-body p-4">
                                <h3 class="mb-4">Send Us a Message</h3>
                                
                                <div class="alert alert-success d-none" id="contact-success">
                                    Your message has been sent successfully. We'll get back to you soon!
                                </div>
                                
                                <div class="alert alert-danger d-none" id="contact-error">
                                    There was an error sending your message. Please try again.
                                </div>
                                
                                <form id="contact-form">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Your Name</label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="subject" class="form-label">Subject</label>
                                        <input type="text" class="form-control" id="subject" name="subject" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="message" class="form-label">Message</label>
                                        <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary btn-lg" id="submit-button">
                                            <span class="spinner-border spinner-border-sm d-none me-2" id="submit-spinner"></span>
                                            Send Message
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Get the buffered content
$content = ob_get_clean();

// Include the layout
include_once __DIR__ . '/layouts/main.php';
?>
