<?php
// Require authentication and set cache headers
require_once __DIR__ . '/../../app/Helpers/PageAuthHelper.php';
App\Helpers\PageAuthHelper::requireAuth();

$title = 'Deposit Funds - CryptoInvest';

// Extra CSS
$extraCss = '<link rel="stylesheet" href="' . BASE_URL . '/assets/css/dashboard.css">';

// Extra JS
$extraJs = '<script src="' . BASE_URL . '/assets/js/deposit.js"></script>';

// Start output buffering
ob_start();
?>

<section class="dashboard-section py-5">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4 mb-lg-0">
                <div class="card border-0 shadow">
                    <!-- User Profile Header -->
                    <div class="card-header bg-white border-0 p-4">
                        <div class="text-center">
                            <div class="avatar-container mb-3">
                                <img src="<?= BASE_URL ?>/assets/images/avatar.png" alt="User Avatar" class="rounded-circle avatar">
                            </div>
                            <h5 class="mb-1" id="user-name">Loading...</h5>
                            <p class="text-muted mb-0" id="user-email">Loading...</p>
                        </div>
                    </div>

                    <!-- Balance Section -->
                    <div class="card-body p-0">
                        <div class="balance-section bg-light p-3 text-center">
                            <p class="text-muted mb-1 small">Available Balance</p>
                            <h4 class="mb-0 text-primary" id="user-balance">$0.00</h4>
                        </div>

                        <!-- Navigation Menu -->
                        <div class="dashboard-menu">
                            <!-- Main Navigation -->
                            <div class="menu-section">
                                <a href="<?= BASE_URL ?>/dashboard" class="menu-item">
                                    <div class="menu-icon">
                                        <i class="fas fa-tachometer-alt"></i>
                                    </div>
                                    <span class="menu-text">Dashboard</span>
                                </a>

                                <a href="<?= BASE_URL ?>/deposit" class="menu-item deposit-action active">
                                    <div class="menu-icon">
                                        <i class="fas fa-plus-circle"></i>
                                    </div>
                                    <span class="menu-text">Deposit Funds</span>
                                    <div class="menu-badge">
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </a>
                                <a href="<?= BASE_URL ?>/investments" class="menu-item">
                                    <div class="menu-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <span class="menu-text">My Investments</span>
                                </a>

                                <a href="<?= BASE_URL ?>/transactions" class="menu-item">
                                    <div class="menu-icon">
                                        <i class="fas fa-exchange-alt"></i>
                                    </div>
                                    <span class="menu-text">Transactions</span>
                                </a>

                                <a href="<?= BASE_URL ?>/profile" class="menu-item">
                                    <div class="menu-icon">
                                        <i class="fas fa-user-cog"></i>
                                    </div>
                                    <span class="menu-text">Profile Settings</span>
                                </a>

                                <a href="<?= BASE_URL ?>/support" class="menu-item">
                                    <div class="menu-icon">
                                        <i class="fas fa-headset"></i>
                                    </div>
                                    <span class="menu-text">Support</span>
                                </a>

                                <a href="<?= BASE_URL ?>/logout" class="menu-item logout-action">
                                    <div class="menu-icon">
                                        <i class="fas fa-sign-out-alt"></i>
                                    </div>
                                    <span class="menu-text">Logout</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">Deposit Funds</h2>
                    <a href="<?= BASE_URL ?>/dashboard" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
                    </a>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow">
                            <div class="card-header bg-white py-3">
                                <h5 class="mb-0">Make a Deposit</h5>
                            </div>
                            <div class="card-body">
                                <form id="deposit-form">
                                    <div class="mb-3">
                                        <label for="amount" class="form-label">Deposit Amount (USD)</label>
                                        <input type="number" class="form-control" id="amount" name="amount" min="10" step="0.01" required>
                                        <div class="form-text">Minimum deposit amount is $10.00</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="cryptocurrency" class="form-label">Select Cryptocurrency</label>
                                        <select class="form-select" id="cryptocurrency" name="cryptocurrency" required>
                                            <option value="">Choose cryptocurrency...</option>
                                            <option value="USDT">USDT (Tether)</option>
                                            <option value="BTC">Bitcoin (BTC)</option>
                                            <option value="ETH">Ethereum (ETH)</option>
                                        </select>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        After clicking "Generate Deposit Address", you'll receive a wallet address to send your funds to.
                                        Your deposit will be credited after network confirmation.
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-wallet me-2"></i> Generate Deposit Address
                                    </button>
                                </form>

                                <!-- Deposit Address Section (Hidden by default) -->
                                <div id="deposit-address-section" class="mt-4" style="display: none;">
                                    <div class="alert alert-success">
                                        <h6 class="alert-heading">Deposit Address Generated</h6>

                                        <!-- Countdown Timer -->
                                        <div class="mb-3 p-3 bg-warning bg-opacity-10 border border-warning rounded">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <div>
                                                    <i class="fas fa-clock text-warning me-2"></i>
                                                    <strong>Time remaining to send deposit:</strong>
                                                </div>
                                                <div class="countdown-timer">
                                                    <span id="countdown-display" class="badge bg-warning text-dark fs-6">30:00</span>
                                                </div>
                                            </div>
                                            <small class="text-muted">
                                                Please send your deposit within the time limit. After expiry, you'll need to generate a new address.
                                            </small>
                                        </div>

                                        <p class="mb-2">Send your <span id="selected-crypto"></span> to the address below:</p>
                                        <div class="d-flex align-items-center mb-3">
                                            <code id="deposit-address" class="me-2 p-2 bg-light border rounded flex-grow-1"></code>
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="copyToClipboard()">
                                                <i class="fas fa-copy"></i> Copy
                                            </button>
                                        </div>

                                        <!-- QR Code placeholder -->
                                        <div class="text-center mb-3">
                                            <div class="bg-light border rounded p-3 d-inline-block">
                                                <i class="fas fa-qrcode fa-3x text-muted"></i>
                                                <div class="small text-muted mt-2">QR Code</div>
                                            </div>
                                        </div>

                                        <hr>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>Important Notes:</strong></p>
                                                <ul class="small text-muted mb-0">
                                                    <li>Only send <span id="selected-crypto-2"></span> to this address</li>
                                                    <li>Minimum deposit: $10.00</li>
                                                    <li>Network confirmations: 1-3 blocks</li>
                                                    <li>Processing time: 5-30 minutes</li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>After Sending:</strong></p>
                                                <ul class="small text-muted mb-0">
                                                    <li>Your deposit will appear as "Pending"</li>
                                                    <li>Funds credited after confirmation</li>
                                                    <li>Check transaction history for updates</li>
                                                    <li>Contact support if issues arise</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card border-0 shadow">
                            <div class="card-header bg-white py-3">
                                <h5 class="mb-0">Deposit Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <small class="text-muted">Minimum Deposit</small>
                                    <h6 class="text-primary">$10.00</h6>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">Processing Time</small>
                                    <h6>1-3 Network Confirmations</h6>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">Deposit Fee</small>
                                    <h6 class="text-success">Free</h6>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">Supported Cryptocurrencies</small>
                                    <div class="mt-2">
                                        <span class="badge bg-primary me-1">USDT</span>
                                        <span class="badge bg-warning me-1">BTC</span>
                                        <span class="badge bg-info">ETH</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card border-0 shadow mt-4">
                            <div class="card-header bg-white py-3">
                                <h5 class="mb-0">Recent Deposits</h5>
                            </div>
                            <div class="card-body">
                                <div id="recent-deposits">
                                    <p class="text-muted text-center">Loading recent deposits...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
$content = ob_get_clean();
require __DIR__ . '/layouts/main.php';
?>
