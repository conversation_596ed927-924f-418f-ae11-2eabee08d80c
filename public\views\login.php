<?php
// Set cache control headers to prevent caching
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// TODO: Re-enable redirect check after debugging
// require_once __DIR__ . '/../../app/Helpers/PageAuthHelper.php';
// App\Helpers\PageAuthHelper::redirectIfAuthenticated();

$title = 'Login - CryptoInvest';

// Extra CSS
$extraCss = '<link rel="stylesheet" href="' . BASE_URL . '/assets/css/auth.css">';

// Extra JS
$extraJs = '<script src="' . BASE_URL . '/assets/js/login.js"></script>';

// Start output buffering
ob_start();
?>

<section class="auth-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card border-0 shadow">
                    <div class="card-body p-4 p-md-5">
                        <div class="text-center mb-4">
                            <img src="<?= BASE_URL ?>/assets/images/logo.png" alt="CryptoInvest Logo" height="50">
                            <h2 class="mt-3 mb-0">Welcome Back</h2>
                            <p class="text-muted">Login to your account</p>
                        </div>

                        <div class="alert alert-danger d-none" id="login-error"></div>

                        <form id="login-form">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username or Email</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary toggle-password" type="button">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                    <label class="form-check-label" for="remember">Remember me</label>
                                </div>
                                <a href="<?= BASE_URL ?>/forgot-password" class="text-decoration-none">Forgot password?</a>
                            </div>

                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary btn-lg" id="login-button">
                                    <span class="spinner-border spinner-border-sm d-none me-2" id="login-spinner"></span>
                                    Login
                                </button>
                            </div>

                            <div class="text-center">
                                <p class="mb-0">Don't have an account? <a href="<?= BASE_URL ?>/register" class="text-decoration-none">Register</a></p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Get the buffered content
$content = ob_get_clean();

// Include the layout
include_once __DIR__ . '/layouts/main.php';
?>
