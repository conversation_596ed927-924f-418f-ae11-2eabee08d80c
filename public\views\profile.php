<?php
// Require authentication and set cache headers
require_once __DIR__ . '/../../app/Helpers/PageAuthHelper.php';
App\Helpers\PageAuthHelper::requireAuth();

$title = 'Profile Settings - CryptoInvest';

// Extra CSS
$extraCss = '<link rel="stylesheet" href="' . BASE_URL . '/assets/css/dashboard.css">';

// Extra JS
$extraJs = '<script src="' . BASE_URL . '/assets/js/profile.js"></script>';

// Start output buffering
ob_start();
?>

<section class="dashboard-section py-5">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4 mb-lg-0">
                <div class="card border-0 shadow">
                    <!-- User Profile Header -->
                    <div class="card-header bg-white border-0 p-4">
                        <div class="text-center">
                            <div class="avatar-container mb-3">
                                <img src="<?= BASE_URL ?>/assets/images/avatar.png" alt="User Avatar" class="rounded-circle avatar">
                            </div>
                            <h5 class="mb-1" id="user-name">Loading...</h5>
                            <p class="text-muted mb-0" id="user-email">Loading...</p>
                        </div>
                    </div>

                    <!-- Balance Section -->
                    <div class="card-body p-0">
                        <div class="balance-section bg-light p-3 text-center">
                            <p class="text-muted mb-1 small">Available Balance</p>
                            <h4 class="mb-0 text-primary" id="user-balance">$0.00</h4>
                        </div>

                        <!-- Navigation Menu -->
                        <div class="dashboard-menu">
                            <!-- Main Navigation -->
                            <div class="menu-section">
                                <a href="<?= BASE_URL ?>/dashboard" class="menu-item">
                                    <div class="menu-icon">
                                        <i class="fas fa-tachometer-alt"></i>
                                    </div>
                                    <span class="menu-text">Dashboard</span>
                                </a>

                                <a href="<?= BASE_URL ?>/deposit" class="menu-item deposit-action">
                                    <div class="menu-icon">
                                        <i class="fas fa-plus-circle"></i>
                                    </div>
                                    <span class="menu-text">Deposit Funds</span>
                                    <div class="menu-badge">
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </a>
                                <a href="<?= BASE_URL ?>/investments" class="menu-item">
                                    <div class="menu-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <span class="menu-text">My Investments</span>
                                </a>

                                <a href="<?= BASE_URL ?>/transactions" class="menu-item">
                                    <div class="menu-icon">
                                        <i class="fas fa-exchange-alt"></i>
                                    </div>
                                    <span class="menu-text">Transactions</span>
                                </a>

                                <a href="<?= BASE_URL ?>/profile" class="menu-item active">
                                    <div class="menu-icon">
                                        <i class="fas fa-user-cog"></i>
                                    </div>
                                    <span class="menu-text">Profile Settings</span>
                                </a>

                                <a href="<?= BASE_URL ?>/support" class="menu-item">
                                    <div class="menu-icon">
                                        <i class="fas fa-headset"></i>
                                    </div>
                                    <span class="menu-text">Support</span>
                                </a>

                                <a href="<?= BASE_URL ?>/logout" class="menu-item logout-action">
                                    <div class="menu-icon">
                                        <i class="fas fa-sign-out-alt"></i>
                                    </div>
                                    <span class="menu-text">Logout</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">Profile Settings</h2>
                    <a href="<?= BASE_URL ?>/dashboard" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
                    </a>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow mb-4">
                            <div class="card-header bg-white py-3">
                                <h5 class="mb-0">Personal Information</h5>
                            </div>
                            <div class="card-body">
                                <form id="profile-form">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="first_name" class="form-label">First Name</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="last_name" class="form-label">Last Name</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="username" name="username" readonly>
                                    </div>
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" readonly>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Update Profile</button>
                                </form>
                            </div>
                        </div>

                        <div class="card border-0 shadow">
                            <div class="card-header bg-white py-3">
                                <h5 class="mb-0">Change Password</h5>
                            </div>
                            <div class="card-body">
                                <form id="password-form">
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label">Current Password</label>
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="new_password" class="form-label">New Password</label>
                                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Change Password</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card border-0 shadow">
                            <div class="card-header bg-white py-3">
                                <h5 class="mb-0">Account Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <img src="<?= BASE_URL ?>/assets/images/avatar.png" alt="Profile Picture" class="rounded-circle" width="80" height="80">
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">Account Balance</small>
                                    <h4 class="text-primary mb-0" id="account-balance">$0.00</h4>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">Account Status</small>
                                    <div>
                                        <span class="badge bg-success" id="account-status">Active</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">Member Since</small>
                                    <div id="member-since">Loading...</div>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">Last Login</small>
                                    <div id="last-login">Loading...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
$content = ob_get_clean();
require __DIR__ . '/layouts/main.php';
?>
