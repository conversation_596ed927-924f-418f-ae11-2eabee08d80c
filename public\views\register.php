<?php
// Set cache control headers to prevent caching
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// TODO: Re-enable redirect check after debugging
// require_once __DIR__ . '/../../app/Helpers/PageAuthHelper.php';
// App\Helpers\PageAuthHelper::redirectIfAuthenticated();

$title = 'Register - CryptoInvest';

// Extra CSS
$extraCss = '<link rel="stylesheet" href="' . BASE_URL . '/assets/css/auth.css">';

// Extra JS
$extraJs = '<script src="' . BASE_URL . '/assets/js/register.js"></script>';

// Start output buffering
ob_start();
?>

<section class="auth-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card border-0 shadow">
                    <div class="card-body p-4 p-md-5">
                        <div class="text-center mb-4">
                            <img src="<?= BASE_URL ?>/assets/images/logo.png" alt="CryptoInvest Logo" height="50">
                            <h2 class="mt-3 mb-0">Create an Account</h2>
                            <p class="text-muted">Join CryptoInvest and start investing</p>
                        </div>

                        <div class="alert alert-danger d-none" id="register-error"></div>

                        <form id="register-form">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                                <div class="invalid-feedback" id="username-error"></div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <div class="invalid-feedback" id="email-error"></div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary toggle-password" type="button">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback" id="password-error"></div>
                                <small class="form-text text-muted">Password must be at least 8 characters long</small>
                            </div>

                            <div class="mb-4">
                                <label for="password_confirmation" class="form-label">Confirm Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                                    <button class="btn btn-outline-secondary toggle-password" type="button">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback" id="password-confirmation-error"></div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                    <label class="form-check-label" for="terms">
                                        I agree to the <a href="<?= BASE_URL ?>/terms" class="text-decoration-none">Terms of Service</a> and <a href="<?= BASE_URL ?>/privacy" class="text-decoration-none">Privacy Policy</a>
                                    </label>
                                    <div class="invalid-feedback" id="terms-error">You must agree to the terms and conditions</div>
                                </div>
                            </div>

                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary btn-lg" id="register-button">
                                    <span class="spinner-border spinner-border-sm d-none me-2" id="register-spinner"></span>
                                    Create Account
                                </button>
                            </div>

                            <div class="text-center">
                                <p class="mb-0">Already have an account? <a href="<?= BASE_URL ?>/login" class="text-decoration-none">Login</a></p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Get the buffered content
$content = ob_get_clean();

// Include the layout
include_once __DIR__ . '/layouts/main.php';
?>
