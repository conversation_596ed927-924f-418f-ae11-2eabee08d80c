<?php
// Require authentication and set cache headers
require_once __DIR__ . '/../../app/Helpers/PageAuthHelper.php';
App\Helpers\PageAuthHelper::requireAuth();

$title = 'Transactions - CryptoInvest';

// Extra CSS
$extraCss = '<link rel="stylesheet" href="' . BASE_URL . '/assets/css/dashboard.css">';

// Extra JS
$extraJs = '<script src="' . BASE_URL . '/assets/js/transactions.js"></script>';

// Start output buffering
ob_start();
?>

<section class="dashboard-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">Transaction History</h2>
                    <a href="<?= BASE_URL ?>/dashboard" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
                    </a>
                </div>

                <div class="card border-0 shadow">
                    <div class="card-header bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">All Transactions</h5>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary active" data-filter="all">All</button>
                                <button type="button" class="btn btn-sm btn-outline-primary" data-filter="deposit">Deposits</button>
                                <button type="button" class="btn btn-sm btn-outline-primary" data-filter="investment">Investments</button>
                                <button type="button" class="btn btn-sm btn-outline-primary" data-filter="earning">Earnings</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Date</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Reference</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="transactions-table">
                                    <tr>
                                        <td colspan="6" class="text-center py-4">Loading transactions...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
$content = ob_get_clean();
require __DIR__ . '/layouts/main.php';
?>
